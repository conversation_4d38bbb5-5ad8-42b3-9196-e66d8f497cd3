package com.desaysv.workserver.controller.test.protocol.service;

import ch.qos.logback.classic.Logger;
import com.desaysv.workserver.TestCaseService;
import com.desaysv.workserver.WebSocketServerListener;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.context.OperationContext;
import com.desaysv.workserver.base.execution.EnhancedExecution;
import com.desaysv.workserver.base.execution.ExecuteResult;
import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.execution.TestResultReport;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.nodes.NodeExecutor;
import com.desaysv.workserver.base.nodes.NodeType;
import com.desaysv.workserver.base.nodes.base.*;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationFailReason;
import com.desaysv.workserver.base.operation.base.OperationGroup;
import com.desaysv.workserver.base.operation.base.OperationsReport;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.CommonOperationMethod;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.suite.ExecutionSuite;
import com.desaysv.workserver.base.variable.JavaLanguageEngine;
import com.desaysv.workserver.filemanager.project.OperationGroupFileManager;
import com.desaysv.workserver.filemanager.project.TestCaseLogFileManager;
import com.desaysv.workserver.protocol.OperationProtocolFactory;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.TestCaseLoggerUtils;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import com.desaysv.workserver.utils.sse.SseUtils;
import com.google.gson.Gson;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Case执行解析器
 */
@Component
@Slf4j
public class ExecutionProtocolInterpreter extends ProtocolInterpreter implements WebSocketServerListener, NodeVisitor, NodeExecutor {
    private final Gson gson = new Gson();

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private ExecutorNotificator executorNotificator;

    @Autowired
    private TestProcessManager testProcessManager;

    @Autowired
    private TestCaseService testCaseService;

    @Autowired
    private OperationProtocolFactory operationProtocolFactory;

    // threadLocal
    private final ThreadLocal<Boolean> pausing = ThreadLocal.withInitial(() -> false);
    private final ThreadLocal<Boolean> failed = ThreadLocal.withInitial(() -> false);

    //全局,使用 volatile 修饰共享变量
    private volatile boolean forcePause = false;
    private volatile boolean allExecutionsStopped = false;
    private volatile boolean singleExecutionStopped = false;
    private volatile boolean taskActivated = false;
    private volatile boolean allowPaused = true;
    private volatile int currentCycle = 0;
    private volatile int historyCycle = 0; //TODO：移动到内部

    private final Lock singleCaseFinishSemaphore = new ReentrantLock();
    private final Object singleExecutionLock = new Object();
    private Future<Map<String, Map<Integer, ExecuteResult>>> caseFuture;

    // 使用一个包含层次信息的类来存储条件状态
    @Data
    private static class IfContext {
        private final String uuid;  // if语句的uuid
        private final int level;    // 嵌套层次
        private boolean shouldExecute; // 当前分支是否应该执行

        public IfContext(String uuid, int level, boolean shouldExecute) {
            this.uuid = uuid;
            this.level = level;
            this.shouldExecute = shouldExecute;
        }

    }

    private final Stack<IfContext> ifStack = new Stack<>();


    @Autowired
    public ExecutionProtocolInterpreter(
            @Qualifier("threadPoolManager") ThreadPoolManager threadPoolManager,
            @Qualifier("executorNotificator") ExecutorNotificator executorNotificator
    ) {
        super(threadPoolManager, executorNotificator);
    }

    /**
     * 测试前准备测试套件执行环境
     */
    private void prepareSuiteEnv() {
        log.info(SPLITTER_LOG);
        log.info("准备测试套件执行环境并清除上次变量");
        allExecutionsStopped = false;
        JavaLanguageEngine.getInstance().clear();
    }

    /**
     * 测试前准备单个执行环境
     */
    private void prepareExecutionEnv(ExecutionContext executionContext, Execution execution) {
        log.info(SPLITTER_LOG);
        log.info("准备当前脚本执行环境");

        //初始化通知器
        executionContext.setOperationContext(execution.getOperationContext());
        executionContext.setTestCycle(execution.getTestCycle());
        executorNotificator.initMainNotificator(executionContext);

        //初始化变量
        pausing.set(false);
        failed.set(false);
        forcePause = false;
        taskActivated = true;
        singleExecutionStopped = false;
        currentCycle = 0;
        allowPaused = executionContext.isPauseWhenTestFailed();

        //查询历史次数
        OperationContext operationContext = executionContext.getOperationContext();
        historyCycle = testCaseService.getTotalTestCycles(
                executionContext.getProjectName(),
                operationContext.getCaseInfo().getModuleName(),
                operationContext.getCaseInfo().getCaseName());
    }

    /**
     * 测试某个项目的脚本名称
     *
     * @param projectName 项目名称
     * @param caseNames   脚本名称集合
     * @return
     */
    @Override
    public Map<String, Map<Integer, ExecuteResult>> interpretByCaseName(String projectName, List<String> caseNames) {
        //获取Case当前项目路径
        List<String> casePaths = new ArrayList<>();
        for (String name : caseNames) {
            String casePath = projectName + File.separator + name;
            casePaths.add(casePath);
        }
        return interpretByCasePaths(casePaths);
    }

    /**
     * 测试单个集合的脚本路径集合
     *
     * @param casePaths 脚本路径集合
     * @return
     */
    public Map<String, Map<Integer, ExecuteResult>> interpretByCasePaths(List<String> casePaths) {
        ExecutionSuite executionSuite = new ExecutionSuite();
        executionSuite.setTestCycle(1);
        List<Execution> executions = new ArrayList<>();
        for (String casePath : casePaths) {
            Execution execution = gson.fromJson(ThreadSafeFileUtils.readFileToString(casePath), Execution.class);
            executions.add(execution);
        }
        executionSuite.setExecutionList(executions);
        return interpret(executionSuite);
    }

    /**
     * 测试整个集合的脚本
     *
     * @param executionSuite
     * @return
     */
    //TODO：支持单个case协议
    @Override
    public Map<String, Map<Integer, ExecuteResult>> interpret(ExecutionSuite executionSuite) {
        //状态查询
        if (taskActivated && caseFuture != null) {
            log.info("终止上一次测试脚本执行任务");
            stopInterpret();
        }
        if (caseFuture != null) {
            //TODO：等待结束测试log打印才能运行
            log.info("上次测试脚本执行任务isDone状态:{}", caseFuture.isDone());
            log.info("上次测试脚本执行任务isCancelled状态:{}", caseFuture.isCancelled());
        }
        //准备测试套件执行环境并清除上次变量
        prepareSuiteEnv();

        //获取测试通知ws
        executorNotificator.addWebSocketServerListener(this);

        //FIXME: testScriptUUID每次都生成一样的问题
        //生成执行上下文
        ExecutionContext executionContext = new ExecutionContext();
        executionContext.setProjectName(executionSuite.getClientInfo().getProjectName());
        executionContext.setUserName(executionSuite.getClientInfo().getUserName());
        executionContext.setClientName(executionSuite.getClientInfo().getClientName());
        executionContext.setUserEmail(executionSuite.getClientInfo().getUserEmail());
        executionContext.setTestSuiteId(executionSuite.getClientInfo().getTestSuiteId());
        executionContext.setPauseWhenTestFailed(executionSuite.getClientInfo().isPauseWhenTestFailed());
        executionContext.setEnableSendingEmail(executionSuite.getClientInfo().isEnableSendingEmail());
        executionContext.setDebugModeEnabled(false);
        executionContext.setTestSuiteName(executionSuite.getClientInfo().getTestSuiteName());

        //测试开始
        testProcessManager.testSuiteStart(executionContext);
        TestCaseLoggerUtils.init();
        //TODO：改成数据库存储
        final Map<String, Map<Integer, ExecuteResult>> results = new LinkedHashMap<>();

        //执行测试
        caseFuture = threadPoolManager.submit(() -> {
            for (int i = 0; i < executionSuite.getTestCycle(); i++) {
                executionContext.setCurrentSuiteLoop(i + 1);
                for (Execution execution : executionSuite.getExecutionList()) {
                    if (allExecutionsStopped) {
                        //所有测试停止
                        return results;
                    }
                    String caseName = execution.getOperationContext().getCaseInfo().getCaseName();
                    results.put(caseName, interpret(executionContext, execution));
                }
            }
            return results;
        });
        try {
            caseFuture.get();
        } catch (CancellationException e) {
            log.warn("用户已取消测试");
            //等待完成
            try {
                synchronized (singleCaseFinishSemaphore) {
                    singleCaseFinishSemaphore.wait();
                }
            } catch (InterruptedException ex) {
                // 捕获到中断异常时，重置中断状态
                Thread.currentThread().interrupt(); // 重置中断状态
                log.warn(ex.getMessage(), ex);
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error(e.getMessage(), e);
            executorNotificator.notifyExceptionToClient(e);
        } finally {
            //测试完成
            log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
            log.info("脚本已结束测试");
            log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
//            if (singleExecutionStopped) {
//                executorNotificator.notifySingleExecutionCompleted();
//            }
            executorNotificator.notifyAllTestCompleted(results);
            testProcessManager.testComplete(executionContext, failed.get(), true);
            threadPoolManager.shutdown();
        }
        return results;
    }

    /**
     * 恢复单步调试
     */
    public void resumeSingleExecution() {
        synchronized (singleExecutionLock) {
            singleExecutionLock.notifyAll();
        }
    }

    /**
     * 执行单个测试脚本
     *
     * @param executionContext 执行上下文
     * @param execution        测试脚本
     * @return
     */
    @Override
    public Map<Integer, ExecuteResult> interpret(ExecutionContext executionContext, Execution execution) throws LoopBreakNotification {
        String caseName = execution.getOperationContext().getCaseName();

        //处理测试步骤为空的情况
        if (execution.getOperationList().isEmpty()) {
            String outputLog = String.format("测试脚本\"%s\"步骤为空", caseName);
            log.info(outputLog);
            SseUtils.outputRunLog(outputLog);
            return new LinkedHashMap<>();
        }
        //初始化日志器
        Logger testCaseLogger = TestCaseLoggerUtils.getLogger();

        //准备日志环境
        File caseLogFolder = TestCaseLogFileManager.of(executionContext.getProjectName(), TestCaseLogFileManager.class).getFolder(executionContext.getTestSuiteName());
        TestCaseLoggerUtils.setAppender(caseName, caseLogFolder);

        //打印当前测试日志
        String outputLog = String.format("测试脚本\"%s\"当前第%d轮测试", caseName, executionContext.getCurrentSuiteLoop());
        SseUtils.outputRunLog(outputLog);
        testCaseLogger.info(outputLog);

        //添加步骤行号
        addLineNo(execution.getOperationList());

        //测试前准备执行环境
        prepareExecutionEnv(executionContext, execution);

        //等待脚本执行
        executorNotificator.notifySingleExecutionStart(execution);
        synchronized (singleExecutionLock) {
            try {
                log.info("等待测试脚本\"{}\"准备就绪", caseName);
                singleExecutionLock.wait();
            } catch (InterruptedException e) {
                log.warn(e.getMessage(), e);
            }
        }

        //分析脚本语法
        EnhancedExecution enhancedExecution = analyseGrammar(execution);

        //清空ifStack
        ifStack.clear();

        //通知编译结果
        if (testProcessManager.compileCompleted(enhancedExecution.getMethodCollector())) {
            return execute(enhancedExecution, executionContext);
        } else {
            testCaseLogger.warn("测试脚本\"{}\"操作步骤编译失败", caseName);
            return new LinkedHashMap<>();
        }
    }

    /**
     * 监控节点状态
     *
     * @param nodeContext
     */
    private void monitorNodeStatus(NodeContext nodeContext) {
        //监听测试脚本执行状态
        nodeContext.setNodeListener(new NodeListener() {
            @Override
            public CycleChangeContext topLayerCycleChangeStarted(int cycle) throws NodeTerminateSignal {
                if (singleExecutionStopped) {
                    throw new NodeTerminateSignal();
                }
                ExecuteResult executeResult = new ExecuteResult();
                executeResult.setTagName(String.format("第%d个顶层循环", cycle));
                executeResult.setOperationResults(new ConcurrentHashMap<>());
                executorNotificator.addTestCycle(cycle - 1, historyCycle + cycle - 1);
                log.info(SPLITTER_LOG);
                String outputLog = String.format("进行第%d个循环测试", cycle);
                log.info(outputLog);
                SseUtils.outputRunLog(SPLITTER_LOG);
                SseUtils.outputRunLog(outputLog);
                //通知测试中的循环变化
                testProcessManager.testing(nodeContext.getExecutionContext(), cycle);
                //TODO：executeResults加入nodeContext中
                //FIXME：减少内存占用，改成Redis
                nodeContext.getExecuteResults().put(cycle, executeResult);
                return executeResult;
            }

            @Override
            public void topLayerCycleChangeEnded() {
//                System.out.println("当前循环->结束");
            }
        });
    }

    private void pairIfElseOperations(Execution execution) {
        // 创建一个内部类来存储 if 语句的上下文信息
        class IfLevelContext {
            final String uuid;
            final int level;

            IfLevelContext(String uuid, int level) {
                this.uuid = uuid;
                this.level = level;
            }
        }
        // 输出开始分析 if-else-endif 结构的信息
        log.info("开始分析并处理 if-else-endif 结构");
        Stack<IfLevelContext> ifStack = new Stack<>();
        List<Operation> operationList = execution.getOperationList();
        int currentLevel = 0;

        // 第一遍遍历：设置 level 和 pairedUuid
        for (Operation operation : operationList) {
            if (operation.getOperationMethod().equals(CommonOperationMethod.IF_EXPRESSION_SUCCESS)) {
                // 设置当前 if 语句的 level
                operation.setLevel(currentLevel);
                // 将当前 if 语句的信息入栈
                ifStack.push(new IfLevelContext(operation.getUuid(), currentLevel));
                currentLevel++; // 进入下一层
            } else if (operation.getOperationMethod().equals(CommonOperationMethod.ELSE_IF)) {
                if (!ifStack.isEmpty()) {
                    IfLevelContext context = ifStack.peek();
                    // 设置当前 else/else-if 语句的 level 为对应 if 语句的 level + 1
                    operation.setLevel(context.level + 1);
                    operation.setPairedUuid(context.uuid);
                } else {
                    log.warn("发现 '否则' 或 '否则如果' 语句，但没有匹配的 '如果' 语句，已跳过！");
                    operation.setLevel(0); // 设置默认层次
                }
            } else if (operation.getOperationMethod().equals(CommonOperationMethod.END_IF)) {
                if (!ifStack.isEmpty()) {
                    IfLevelContext context = ifStack.pop();
                    operation.setLevel(context.level);
                    currentLevel = context.level; // 返回上一层
                } else {
                    log.warn("发现 '结束如果' 语句，但没有匹配的 '如果' 语句，已跳过！");
                    operation.setLevel(0); // 设置默认层次
                }
            } else {
                // 普通操作继承当前层次
                operation.setLevel(currentLevel);
            }
        }
        // 输出 if-else-endif 结构分析处理完成的信息
        log.info("if-else-endif 结构分析处理完成");
    }

    private void printIfElseTree(List<Operation> operationList) {
        log.info("打印 if-else-endif 结构及层次信息：");
        for (Operation operation : operationList) {
            String prefix = generatePrefix(operation.getLevel()); // 根据层次缩进
            if (operation.getOperationMethod().equals(CommonOperationMethod.IF_EXPRESSION_SUCCESS)) {
                log.info("{}├── *如果*: uuid={}, level={}", prefix, operation.getUuid(), operation.getLevel());
            } else if (operation.getOperationMethod().equals(CommonOperationMethod.ELSE_IF)) {
                log.info("{}├── *否则*: pairedUuid={}, level={}", prefix, operation.getPairedUuid(), operation.getLevel());
            } else if (operation.getOperationMethod().equals(CommonOperationMethod.END_IF)) {
                log.info("{}└── *结束如果*: level={}", prefix, operation.getLevel());
            } else {
                log.info("{}└── 操作: {}, uuid={}, level={}", prefix, operation, operation.getUuid(), operation.getLevel());
            }
        }
        log.info("if-else-endif 结构树打印完成");
    }

    private String generatePrefix(int level) {
        StringBuilder prefixBuilder = new StringBuilder();
        for (int i = 0; i < level; i++) {
            prefixBuilder.append("    ");
        }
        return prefixBuilder.toString();
    }


    /**
     * 通过节点封装测试脚本执行
     *
     * @param enhancedExecution 增强的执行体
     * @param executionContext  执行上下文
     * @return 测试结果集合
     */
    public Map<Integer, ExecuteResult> execute(EnhancedExecution enhancedExecution,
                                               ExecutionContext executionContext) throws LoopBreakNotification {
        String caseName = executionContext.getOperationContext().getCaseInfo().getCaseName();

        //打印开始执行脚本信息
        String outputLog = String.format("开始执行测试脚本:%s", caseName);
        TestCaseLoggerUtils.getLogger().info(outputLog);
        SseUtils.outputRunLog(outputLog);
        log.info("测试脚本\"{}\"信息:{}", caseName, enhancedExecution);

        //配置执行次数
        Execution execution = enhancedExecution.getExecution();
        int testCycle = executionContext.getTestCycle();
        boolean infinite = testCycle == -1;
        if (infinite) {
            String log = String.format("测试脚本\"%s\"执行无限循环", caseName);
            SseUtils.outputRunLog(log);
            TestCaseLoggerUtils.getLogger().info(log);
        } else {
            String log = String.format("测试脚本\"%s\"总共要求执行%d次", caseName, testCycle);
            SseUtils.outputRunLog(log);
            TestCaseLoggerUtils.getLogger().info(log);
        }

        //开始测试脚本回调
        testProcessManager.testcaseStart(executionContext);

        //创建节点
        NodeContext nodeContext = createNodeContext(enhancedExecution, executionContext);

        //设置脚本名称
        executorNotificator.getExecutionPreview().getNotification().setCaseName(caseName);

        // 在执行节点之前为  if-else 语句设置 pairedUuid
        pairIfElseOperations(execution);

        // 打印 if-else 结构
        printIfElseTree(execution.getOperationList());
        try {
            //监控节点状态
            monitorNodeStatus(nodeContext);

            //执行测试脚本
            executeByNode(nodeContext.getNode(), nodeContext);
        } finally {
            // 清空 ifStack
            ifStack.clear();
            //记录测试报告
            TestResultReport currentTestReport = TestResultReport.reportSingleLoop(nodeContext.getExecuteResults());

            //打印失败率
            String failRateLog = String.format("失败率:%s", currentTestReport);
            SseUtils.outputRunLog(failRateLog);
            TestCaseLoggerUtils.getLogger().info(failRateLog);

            //打印结束执行脚本信息
            String endTestLog = String.format("单个测试脚本测试结束:%s", caseName);
            SseUtils.outputRunLog(endTestLog);
            TestCaseLoggerUtils.getLogger().info(endTestLog);
            SseUtils.outputRunLog(SPLITTER_LOG);
            TestCaseLoggerUtils.getLogger().info(SPLITTER_LOG);

            //取最新汇总结果，针对case脚本多个循环
            TestResultReport recentSummaryTestResultReport = executionContext.getCurrentTestResultReportMap().get(caseName);
            TestResultReport testResultReport = TestResultReport.reportAllLoop(recentSummaryTestResultReport, currentTestReport);
            executorNotificator.setTestResultReport(testResultReport);
            executionContext.getCurrentTestResultReportMap().put(caseName, testResultReport);//汇总报告结果存起来
            log.info("更新测试脚本执行结果:{}(第{}行)->{}",
                    executorNotificator.getExecutionPreview().getNotification().getCaseName(),
                    executorNotificator.getExecutionPreview().getNotification().getExecutionIndex(),
                    testResultReport);
            //结束单个测试脚本通知
            executorNotificator.notifySingleExecutionCompleted();

            //结束单个测试脚本回调
            testProcessManager.testCaseComplete(nodeContext.getExecutionContext(), failed.get());
            synchronized (singleCaseFinishSemaphore) {
                singleCaseFinishSemaphore.notifyAll();
            }
        }
        return nodeContext.getExecuteResults();
    }

    @Override
    public void executeByNode(LoopNode node) throws LoopBreakNotification {
        executeByNode(node, null);
    }

    @Override
    public void executeByNode(LoopNode node, NodeContext nodeContext) throws LoopBreakNotification {
        node.accept(this, nodeContext);
    }

    @Override
    public boolean visit(LoopNode node, NodeContext nodeContext) throws LoopBreakNotification {
        if (singleExecutionStopped) {
            //终止测试执行
            return false;
        }
        Operation operation = node.getOperation();
        if (operation == null) {
            return true;
        }
        return handleConditionalLogic(nodeContext, operation);
    }

    /**
     * 完成单个测试用例脚本
     *
     * @param node 测试脚本节点
     */
    @Override
    public void visitCompleted(LoopNode node, NodeContext nodeContext) {
        if (node.getNodeType() == NodeType.OPERATION_NODE) {
            nodeContext.getExecuteResults().values().forEach(ExecuteResult::selfCheck);
            //增加测试次数
            executorNotificator.increaseTestCycle();
            taskActivated = false;
        } else {
            TestCaseLoggerUtils.getLogger().info("结束测试步骤节点:{}", node);
        }
    }


    @Override
    protected void distributeOperationBegin(Operation operation) {
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~第{}行步骤执行开始", operation.getLineNo() + 1);
        String outputLog = String.format("执行步骤: %s", operation);
        log.info(outputLog);
        SseUtils.outputRunLog(outputLog);
    }

    @Override
    protected void distributeOperationEnd(Operation operation) {
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~第{}行步骤执行结束", operation.getLineNo() + 1);
    }


    private boolean handleConditionalLogic(NodeContext nodeContext, Operation operation) throws LoopBreakNotification {
        boolean shouldExecute = true;
        OperationMethod operationMethod = operation.getOperationMethod();

        if (operationMethod.equals(CommonOperationMethod.IF_EXPRESSION_SUCCESS)) {
            // IF语句
            if (ifStack.isEmpty() || ifStack.peek().shouldExecute) {  // 检查当前分支是否应该被执行
                boolean condition = wasPreviousOperationSuccessful(nodeContext, operation.getUuid());
                IfContext context = new IfContext(operation.getUuid(), operation.getLevel(), condition);
                ifStack.push(context);
                shouldExecute = condition;
            } else {
                shouldExecute = false; // 如果当前分支不应该被执行，则不计算条件，并跳过
            }
        } else if (operationMethod.equals(CommonOperationMethod.ELSE_IF)) {
            // ELSE IF语句
            if (!ifStack.isEmpty()) {
                IfContext currentContext = ifStack.peek();
                // 检查是否匹配当前层次的if语句
                if (currentContext.level == operation.getLevel() - 1 &&
                        currentContext.uuid.equals(operation.getPairedUuid())) {
                    //只有当上一个if为false时才执行
                    shouldExecute = !currentContext.shouldExecute;
                    currentContext.shouldExecute = shouldExecute;
                } else {
                    // 如果 pairedUuid 不匹配，说明不在同一个逻辑分支，直接跳过
                    shouldExecute = false;
                }
            } else {
                log.warn("发现 '否则如果' 语句，但没有匹配的 '如果' 语句，已跳过！");
                shouldExecute = false;
            }
        } else if (operationMethod.equals(CommonOperationMethod.END_IF)) {
            // ENDIF语句
            if (!ifStack.isEmpty() &&
                    ifStack.peek().level == operation.getLevel()) {
                ifStack.pop();
            } else {
                log.warn("发现 '结束如果' 语句，但没有匹配的 '如果' 语句，已跳过！");
            }
        } else {
            // 普通语句，检查当前栈顶的shouldExecute，如果栈为空，则直接执行。
            if (!ifStack.isEmpty()) {
                IfContext currentContext = ifStack.peek();
//                log.info("currentContext:{}", currentContext);
                // 只有当前层次的if的shouldExecute=true才能执行
                shouldExecute = currentContext.shouldExecute;
            }
        }

        if (shouldExecute) {
            return beginExecuteOperation(operation, nodeContext);
        } else {
            log.info("跳过操作 {}, 条件不满足", operation);
            return true;
        }
    }

    private boolean wasPreviousOperationSuccessful(NodeContext nodeContext, String currentUuid) {
        ExecuteResult executeResult = (ExecuteResult) nodeContext.getCycleChangeContext();
        if (executeResult == null || executeResult.getOperationResultsByUuid() == null) {
            return false;
        }

        List<Operation> operationList = nodeContext.getOperationList();
        if (operationList == null || operationList.isEmpty()) {
            return false;
        }

        int currentIndex = -1;
        for (int i = 0; i < operationList.size(); i++) {
            if (operationList.get(i).getUuid().equals(currentUuid)) {
                currentIndex = i;
                break;
            }
        }

        if (currentIndex == -1) {
            // Should not happen if currentUuid is valid
            return false;
        }

        OperationResult previousResult = null;
        for (int i = currentIndex - 1; i >= 0; i--) {
            Operation prevOperation = operationList.get(i);
            if (!prevOperation.isAnnotated()) { // Check if the operation is not a comment
                Map<String, OperationResult> resultMap = executeResult.getOperationResultsByUuid();
                previousResult = resultMap.get(prevOperation.getUuid());
                break; // Found the previous non-comment operation
            }
        }

        return previousResult != null && previousResult.isOk();
    }

    /**
     * 组装失败日志
     *
     * @param errorBuilder
     * @param nodeContext
     * @param operation
     * @param executeTimes
     * @param operationResult
     */
    private void generateErrorLog(StringBuilder errorBuilder, NodeContext nodeContext, Operation operation,
                                  int executeTimes, OperationResult operationResult) {
        TestCaseLoggerUtils.getLogger().info(SPLITTER_LOG);
        String posContext = String.format("测试总结：第%d轮测试, %s层嵌套（第%d个循环）,第%d行步骤",
                nodeContext.getFirstLayerTestCycle(),
                nodeContext.getLayerCycle().getLayer() == 0 ? "顶" : String.format("第%d", nodeContext.getLayerCycle().getLayer() + 1),
                nodeContext.getLayerCycle().getCycle(),
                operation.getParentLineNo() + 1);
        SseUtils.outputRunLog(posContext);
        TestCaseLoggerUtils.getLogger().warn(posContext);

        OperationContext operationContext = nodeContext.getExecutionContext().getOperationContext();
        String fileInfo = String.format("测试脚本:%s", operationContext.getCaseName());
        errorBuilder.append(fileInfo).append("\n");
        errorBuilder.append(posContext).append("\n");

        int hasExecuteTimes = executeTimes + 1;
        String errorContext = String.format("操作步骤：%s->%s%s",
                operation.getOperationMethod().getMethodName(),
                operation.getOperationObject(),
                hasExecuteTimes == 1 ? "" : String.format("，执行失败%d次", hasExecuteTimes));
        SseUtils.outputRunLog(errorContext);
        TestCaseLoggerUtils.getLogger().warn(errorContext);
        errorBuilder.append(errorContext).append("\n");
        if (operationResult.getMessage() == null) {
            operationResult.setMessage("未知原因，联系开发者排查");
        }
        String errorReason = String.format("失败原因:%s", operationResult.getMessage());
        SseUtils.outputRunLog(errorReason);
        TestCaseLoggerUtils.getLogger().warn(errorReason);
        errorBuilder.append(errorReason).append("\n");
    }

    private void retryCallbackCheck(boolean enterRetryCallback, NodeContext nodeContext, Operation
            operation, OperationResult operationResult) throws LoopBreakNotification {
        if (!enterRetryCallback && operationResult.isFailed()) {
            if (nodeContext.isFailCallback()) {
                OperationGroupFileManager operationGroupFileManager = OperationGroupFileManager.of(nodeContext.getExecutionContext().getProjectName(), OperationGroupFileManager.class);
                OperationGroup operationGroup = operationGroupFileManager.loadOperationGroup(RETRY_CALLBACK);
                if (operationGroup != null) {
                    log.info("#####################################");
                    String mLog = "进入失败重试逻辑";
                    log.info(mLog);
                    SseUtils.outputRunLog(mLog);
                    for (Operation subOperation : operationProtocolFactory.product(operationGroupFileManager.loadJsonOperationGroup(operationGroup))) {
                        //FIXME：subOperation不支持内循环
                        subOperation.setParentLineNo(operation.getLineNo());
                        distributeOperation(nodeContext.getNode(), nodeContext, subOperation, true);
                    }
                }
            }
        }
    }

    private void checkIfNeedToPause(NodeContext nodeContext, Operation operation, OperationResult operationResult, StringBuilder errorBuilder) {
        if (!singleExecutionStopped) {
            if (forcePause) {
                log.warn("用户请求暂停！");
                executorNotificator.getExecutionPreview().getNotification().setUserPausing(true);
                pausing.set(true);
                executorNotificator.notifyPausingToClient(operationResult);
                threadPoolManager.pause();
                threadPoolManager.pauseIfNecessary();
                log.info("收到用户请求，程序恢复!");
            } else if (operationResult.isPauseRequested()) {
                log.warn("操作请求暂停: {}", operationResult.getMessage());
                pausing.set(true);
                executorNotificator.notifyPausingToClient(operationResult);
                threadPoolManager.pause();
                threadPoolManager.pauseIfNecessary();
                log.info("收到操作请求，程序暂停!");
            } else if (allowPaused && operationResult.isFailed()) {
                // Log the next operation
                List<Operation> operationList = nodeContext.getOperationList();
                int currentIndex = operation.getLineNo();
                boolean ifOperationExist = false;
                if (operationList != null && currentIndex < operationList.size()) { // Ensure currentIndex is within bounds
                    Operation nextOperation = operationList.stream()
                            .skip(currentIndex + 1L)
                            .filter(op -> op != null && !op.isAnnotated())
                            .findFirst()
                            .orElse(null);
                    if (nextOperation != null) {
                        // 使用与该方法中其他日志记录一致的记录器
                        log.info("检测到下一行操作:{}", nextOperation);
                        ifOperationExist = nextOperation.getOperationMethod().getKeyword().equalsIgnoreCase(CommonOperationMethod.IF_EXPRESSION_SUCCESS.getKeyword());
                    }
                }
                if (!ifOperationExist) {
                    //如果下一行没有如果
                    failed.set(true);
                    log.warn("执行失败，线程\"{}\"暂停！", Thread.currentThread().getName());
                    // 通知失败
                    OperationFailReason reason = new OperationFailReason(operationResult.getMessage(), errorBuilder.toString());
                    testProcessManager.testFailed(nodeContext.getExecutionContext(), currentCycle, reason);
                    pausing.set(true);
                    executorNotificator.notifyPausingToClient(operationResult);
                    threadPoolManager.pause();
                    log.info("执行失败，程序暂停!");
                    threadPoolManager.pauseIfNecessary();
                }
            } else {
                OperationContext operationContext = nodeContext.getExecutionContext().getOperationContext();
                waitSeconds(operationContext.getAfterWait());
            }
        }
    }


    private void updateOperationReport(NodeContext nodeContext, Operation operation, OperationResult operationResult) {
        ExecuteResult executeResult = ((ExecuteResult) nodeContext.getCycleChangeContext());
        executeResult.putOperationResult(operation.getParentLineNo(), operationResult);
        //发送当前步骤结果
        int row = operation.getParentLineNo();
        OperationsReport operationsReport = OperationsReport.report(row, nodeContext.getExecuteResults());
        operationsReport.setTestResult(operationResult.getResult());
        executorNotificator.notifyOperationsReport(row, operationsReport);
    }

    /**
     * 执行操作
     *
     * @param node
     * @param nodeContext
     * @param operation
     * @param enterRetryCallback
     * @return
     */
    @Override
    public boolean executeOperation(LoopNode node,
                                    NodeContext nodeContext,
                                    Operation operation,
                                    boolean enterRetryCallback) throws LoopBreakNotification {
        if (!enterRetryCallback) {
            // 发送当前测试步骤行
            executorNotificator.notifyTestingStatus(operation, node);
            threadPoolManager.pauseIfNecessary();
        }
        // 检测是否为内置语法
        if (checkIfBuiltinGrammar(nodeContext, operation)) {
            return true;
        }
        // 前置等待时间
        OperationContext operationContext = nodeContext.getExecutionContext().getOperationContext();
        waitSeconds(operationContext.getBeforeWait());

        // 开始执行步骤
        StringBuilder errorBuilder = new StringBuilder();
        OperationResult operationResult;
        try {
            int executeTimes = 0;
            do {
                // 打印重试日志
                if (executeTimes > 0) {
                    String outputLog = String.format("操作步骤重试第%d次:%s", executeTimes, operation);
                    SseUtils.outputRunLog(outputLog);
                    TestCaseLoggerUtils.getLogger().info(outputLog);
                }
                // 执行步骤开始

                operationResult = interpret(nodeContext.getExecutionContext(), operation);

                // 执行失败打印
                if (!enterRetryCallback && operationResult.isFailed()) {
                    generateErrorLog(errorBuilder, nodeContext, operation, executeTimes, operationResult);
                }
                executeTimes++;
            } while (operationResult.isFailed() && operationResult.isRetry() && executeTimes < operationResult.getRetryTimes());

            // 检测是否需要失败重试
            retryCallbackCheck(enterRetryCallback, nodeContext, operation, operationResult);
        } catch (Throwable e) {
            String exceptionReason = String.format("步骤执行出错，操作信息:%s,%s", operationContext, operation);
            SseUtils.outputRunLog(exceptionReason);
            TestCaseLoggerUtils.getLogger().error(exceptionReason, e);
            operationResult = new OperationResult();
            operationResult.setOk(false);
            operationResult.setMessage(ExceptionUtils.getExceptionString(e));
            operationResult.setData(e.getMessage());
            errorBuilder.append(exceptionReason).append("\n");
        }
        operationResult.setUuid(operation.getUuid());
        operationResult.setContext(operation.toString());
        if (!enterRetryCallback) {
            // 更新步骤测试结果
            updateOperationReport(nodeContext, operation, operationResult);
            // 暂停判断
            checkIfNeedToPause(nodeContext, operation, operationResult, errorBuilder);
        }
        // 打印测试结果
        String info = String.format("步骤执行%s: %s", operationResult.isOk() ? "成功" : "失败", operation);
        SseUtils.outputRunLog(info);
        log.info(info);
        return operationResult.isOk();
    }

    @Override
    public void onWebSocketError() {

    }

    @Override
    public void onWebSocketClose() {

    }

    @Override
    public boolean stopInterpret() {
        forcePause = false;
        taskActivated = false;
        singleExecutionStopped = true;
        allExecutionsStopped = true;
        //通知测试终止
        testProcessManager.testTerminated();
        caseFuture.cancel(true); //cancel(true)会中断当前线程，如果某个任务不需要被中断，需要放在另外线程或者异步管理
        TestCaseLoggerUtils.getLogger().info("强制停止完成");
        return true;
    }

    @Override
    public boolean pauseInterpret() {
        //通知暂停
        testProcessManager.testPausing();
        forcePause = true;
        threadPoolManager.pause(); // 这会让所有线程在beforeExecute时暂停
        return false;
    }

    @Override
    public boolean resumeInterpret() {
        forcePause = false;
        threadPoolManager.resume();
        //通知恢复
        testProcessManager.testResume();
        return true;
    }

}
