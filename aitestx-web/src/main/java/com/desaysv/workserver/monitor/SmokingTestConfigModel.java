package com.desaysv.workserver.monitor;

import lombok.Data;
import lombok.Getter;

@Data
public class SmokingTestConfigModel {
    @Getter
    public enum UpgradeFileType {
        S19(0), HEX(1), BIN(2);

        private final int value;

        UpgradeFileType(int value) {
            this.value = value;
        }

    }

    private boolean isListeningUpgradeNotification;
    private UpgradeFileType fileType;
    private String smokingTestReportPath;
    private String version;//版本号
    private String reportUrl;//报告网盘地址
    private String reportPassRate;
    private boolean modulePassRateRequired;

    public UpgradeFileType getFileType(String suffix) {
        if (suffix.equals(".s19")) {
            return UpgradeFileType.S19;
        } else if (suffix.equals(".hex")) {
            return UpgradeFileType.HEX;
        } else {
            return UpgradeFileType.BIN;
        }
    }

}
