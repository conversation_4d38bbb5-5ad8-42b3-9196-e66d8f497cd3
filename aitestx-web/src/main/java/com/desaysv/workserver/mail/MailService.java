package com.desaysv.workserver.mail;

import com.desaysv.workserver.base.manager.ClientInfoObserver;
import com.desaysv.workserver.base.manager.ClientInfoReceiveFromClient;
import com.desaysv.workserver.controller.cicd.AutomaticUpgradeController;
import com.desaysv.workserver.entity.UpgradeResultReportDto;
import com.desaysv.workserver.monitor.PolyTestUsageStatusUpdater;
import com.desaysv.workserver.monitor.SmokingTestConfigModel;
import com.desaysv.workserver.monitor.SmokingTestConfigObserver;
import com.desaysv.workserver.utils.DateUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@Slf4j
@Service
//no Lazy
public class MailService implements ClientInfoObserver, SmokingTestConfigObserver {
    @Autowired
    private TemplateEngine templateEngine;
    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private PolyTestUsageStatusUpdater polyTestUsageStatusUpdater;
    @Autowired
    private AutomaticUpgradeController automaticUpgradeController;
    private final Executor asyncExecutor;
    private ClientInfoReceiveFromClient clientInfoReceiveFromClient;
    private SmokingTestConfigModel smokingTestConfigModel;

    @PostConstruct
    private void init() {
        polyTestUsageStatusUpdater.addObserver(this);
        automaticUpgradeController.addObserver(this);

    }

    public MailService() {
        asyncExecutor = Executors.newSingleThreadExecutor();
    }

    /**
     * 发送邮件方法
     *
     * @param mailTestDto
     * @return
     */
    public boolean sendEmail(TestResultReportDto mailTestDto) {
        asyncExecutor.execute(() -> {
            if (mailTestDto.isFunctionMode()) {
                mailUtils.sendEmail(getFunctionTestMailParamDto(mailTestDto));
            } else {
                mailUtils.sendEmail(getSmokeTestMailParamDto(mailTestDto));

            }
        });
        return true;
    }

    /**
     * 发送自动升级结果通知邮件方法
     *
     * @param upgradeResultReportDto
     * @return
     */
    public boolean sendUpgradeResultReportEmail(UpgradeResultReportDto upgradeResultReportDto) {
        asyncExecutor.execute(() -> {
            mailUtils.sendEmail(getUpgradeFailMailParamDto(upgradeResultReportDto));
        });
        return true;
    }


    /**
     * 将文件复制到指定的网盘路径
     *
     * @param localFile 本地文件路径
     * @param reportUrl 目标网盘路径
     * @throws IOException 如果复制过程中出现错误
     */
    private void copyFileToNetworkDrive(String localFile, String reportUrl) throws IOException {
        Path sourcePath = Paths.get(localFile);
        Path targetPath = Paths.get(reportUrl, sourcePath.getFileName().toString());

        // 检查目标文件是否已存在，存在则覆盖
        Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
    }

    /**
     * 计算NG还是GO
     *
     * @param executedCases  执行的case
     * @param passCases      通过的case
     * @param reportPassRate 目标通过率
     * @return
     */
    private String calculateConclusion(int executedCases, int passCases, int reportPassRate) {
        double passRate = executedCases == 0 ? 0 : ((double) passCases / executedCases) * 100;
        return passRate >= (reportPassRate != 0 ? reportPassRate : 90.0) ? "GO" : "NG";  // 如果通过率大于等于90%，结论为“GO”，否则为“NG”
    }

    private MailParamDto getFunctionTestMailParamDto(TestResultReportDto mailTestDto) {
        // 计算汇总信息
        int totalCases = 0;
        int totalPass = 0;
        for (TestResult result : mailTestDto.getFunctionTestResults()) {
            totalCases += result.getExecutedCases();
            totalPass += result.getPassCases();
        }
        int totalFail = totalCases - totalPass;
        String overallPassRate = totalCases == 0 ? "0%" : String.format("%.2f%%", ((double) totalPass / totalCases) * 100);
        // 准备模板数据
        Context context = new Context();
        context.setVariable("project", clientInfoReceiveFromClient.getProject());
        context.setVariable("version", smokingTestConfigModel.getVersion());
        context.setVariable("passRate", overallPassRate);
        context.setVariable("reportPath", mailTestDto.getCloudReportPath());
        context.setVariable("startTime", mailTestDto.getStartTime());
        context.setVariable("endTime", mailTestDto.getEndTime());
        context.setVariable("testResults", mailTestDto.getFunctionTestResults());
        context.setVariable("totalCases", totalCases);
        context.setVariable("totalPass", totalPass);
        context.setVariable("totalFail", totalFail);
        context.setVariable("overallPassRate", overallPassRate);
        context.setVariable("totalConclusion", smokingTestConfigModel.isModulePassRateRequired() ? calculateAllModulePassRate(mailTestDto.getFunctionTestResults(), mailTestDto.getPassRate()) : calculateConclusion(totalCases, totalPass, mailTestDto.getPassRate()));
        String emailContent = templateEngine.process("function_test_report", context);

        MailParamDto mailParamDto = new MailParamDto();
        mailParamDto.setSubject(String.format("%s/%s-%s-%s功能序列化测试结果通知", clientInfoReceiveFromClient.getProject(),
                clientInfoReceiveFromClient.getUserName(),
                clientInfoReceiveFromClient.getClient(),
                clientInfoReceiveFromClient.getTestUnit()));
        String[] emails = clientInfoReceiveFromClient.getEmails();
        if (emails == null) {
            emails = new String[0];
        }
        String[] allEmails = new String[emails.length + 1];
        System.arraycopy(emails, 0, allEmails, 0, emails.length);
        allEmails[emails.length] = clientInfoReceiveFromClient.getEmail();
        mailParamDto.setRecipients(allEmails);
        mailParamDto.setContent(emailContent);

        // 使用 File 数组添加附件
        List<File> files = new ArrayList<>();
        File file = new File(mailTestDto.getLocalReportPath());
        if (file.exists()) {
            files.add(file);
        }
        mailParamDto.setFiles(files.toArray(new File[0]));
        return mailParamDto;
    }

    /**
     * 计算所有模块的通过率(只要有一个模块不通过，则返回NO)
     * @param functionTestResults
     * @param reportPassRate
     * @return
     */
    private String calculateAllModulePassRate(List<TestResult> functionTestResults, int reportPassRate) {
        String result = "GO";
        for (TestResult testResult : functionTestResults) {
            if (testResult.getPassRateAsInt() < reportPassRate) {
                result = "NG";
                break;
            }
        }
        return result;
    }


    private MailParamDto getSmokeTestMailParamDto(TestResultReportDto mailTestDto) {
        TestResult smokeTestResults = mailTestDto.getSmokeTestResults();
        // 准备模板数据
        Context context = new Context();
        context.setVariable("project", clientInfoReceiveFromClient.getProject());
        context.setVariable("version", mailTestDto.getVersion());
        context.setVariable("passRate", smokeTestResults.getPassRate());
        context.setVariable("reportPath", mailTestDto.getCloudReportPath());
        context.setVariable("startTime", mailTestDto.getStartTime());
        context.setVariable("endTime", mailTestDto.getEndTime());
        context.setVariable("totalCases", smokeTestResults.getExecutedCases());
        context.setVariable("totalPass", smokeTestResults.getPassCases());
        context.setVariable("totalFail", smokeTestResults.getFailCases());
        context.setVariable("overallPassRate", smokeTestResults.getPassRate());
        context.setVariable("conclusion", smokeTestResults.getConclusion());

        String emailContent = templateEngine.process("smoke_test_report", context);

        MailParamDto mailParamDto = new MailParamDto();
        mailParamDto.setSubject(String.format("%s/%s-%s-%s仪表自动冒烟点检结果通知", clientInfoReceiveFromClient.getProject(),
                clientInfoReceiveFromClient.getUserName(),
                clientInfoReceiveFromClient.getClient(),
                clientInfoReceiveFromClient.getTestUnit()));
        String[] emails = clientInfoReceiveFromClient.getEmails();
        if (emails == null) {
            emails = new String[0];
        }
        String[] allEmails = new String[emails.length + 1];
        System.arraycopy(emails, 0, allEmails, 0, emails.length);
        allEmails[emails.length] = clientInfoReceiveFromClient.getEmail();
        mailParamDto.setRecipients(allEmails);
        mailParamDto.setContent(emailContent);

        // 使用 File 数组添加附件
        List<File> files = new ArrayList<>();
        File file = new File(mailTestDto.getLocalReportPath());
        if (file.exists()) {
            files.add(file);
        }
        mailParamDto.setFiles(files.toArray(new File[0]));

        return mailParamDto;
    }

    public static void main(String[] args) {
        //功能测试
        List<TestResult> testResults = Arrays.asList(
                new TestResult(0, "仪表电源", 10, 9, 90),  // 90% 结论为GO
                new TestResult(1, "信号外发", 12, 10, 90) // 83.33% 结论为NG
        );
        TestResultReportDto mailTestDto = new TestResultReportDto( true, "2024-09-23 xx:xx:xx", DateUtils.getNow(),
                "D:\\FlyTest\\data\\client\\projects\\项目管理\\report\\功能测试\\09_GAC A19 IDC_车身域-指示灯_TestCase_V18测试报告_20240923_18-53-50.xlsx", "", "QNX 6.6.0", "SOC_V1.0", "MCU_V1.0", "V18",
                testResults, 90);
        //冒烟点检
        TestResult mailTestResult = new TestResult(10, 9, 90);
        TestResultReportDto mailTestDto2 = new TestResultReportDto( true,
                "2024-09-23 xx:xx:xx", DateUtils.getNow(), "D:\\FlyTest\\data\\client\\projects\\项目管理\\report\\功能测试\\09_GAC A19 IDC_车身域-指示灯_TestCase_V18测试报告_20240923_18-53-50.xlsx", null, "QNX 6.6.0", "SOC_V1.0", "MCU_V1.0", "V18"
                , mailTestResult, 90);
        MailService mailService = SpringContextHolder.getBean(MailService.class);
        //功能测试
        mailService.sendEmail(mailTestDto);
        mailService.sendEmail(mailTestDto2);
    }

    @Override
    public void update(ClientInfoReceiveFromClient clientInfo) {
        this.clientInfoReceiveFromClient = clientInfo;
    }

    @Override
    public void update(SmokingTestConfigModel configModel) {
        this.smokingTestConfigModel = configModel;
    }


    private MailParamDto getUpgradeFailMailParamDto(UpgradeResultReportDto upgradeResultReportDto) {
        String[] testUnitArr = clientInfoReceiveFromClient.getTestUnit().split("/");
        upgradeResultReportDto.setTestUnit(testUnitArr[0]);
        upgradeResultReportDto.setProjectName(clientInfoReceiveFromClient.getProject());
        String subject = String.format("%s/%s-%s cicd流水线升级暂停",
                upgradeResultReportDto.getProjectName(), upgradeResultReportDto.getVersion(), upgradeResultReportDto.getTestUnit());
        MailParamDto mailParamDto = new MailParamDto();
        mailParamDto.setContent(String.format("<html><p>Dear all：<br>%s，%s版本，本次冒烟测试升级失败。<br>测试电脑：%s<br>失败原因：%s<br>失败时间：%s<br>请排查，谢谢！</p></html>",
                upgradeResultReportDto.getProjectName(), upgradeResultReportDto.getVersion(), upgradeResultReportDto.getTestUnit(), upgradeResultReportDto.getErrorMessage(),
                upgradeResultReportDto.getEndTime()));
        String[] emails = clientInfoReceiveFromClient.getEmails();
        if (emails == null) {
            emails = new String[0];
        }
        String[] allEmails = new String[emails.length + 1];
        System.arraycopy(emails, 0, allEmails, 0, emails.length);
        allEmails[emails.length] = clientInfoReceiveFromClient.getEmail();
        mailParamDto.setRecipients(allEmails);
        mailParamDto.setSubject(subject);
        return mailParamDto;
    }
}
