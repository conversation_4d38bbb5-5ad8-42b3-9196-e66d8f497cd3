<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>仪表自动冒烟点检测试报告</title>
    <style>
        .header {
            background-color: #d3d3d3; /* 灰色 */
        }
        .highlight {
            color: red; /* 高亮文本颜色 */
            background-color: yellow; /* 黄色 */
        }
        .table {
            border-collapse: collapse;
        }
        .table, .table th, .table td {
            border: 1px solid black;
        }
        .table th, .table td {
            padding: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
<p>各位好：</p>
<p>
    <span th:text="${project}">XXX项目</span> <span th:text="${version}">XX</span> 版本仪表自动冒烟点检测已完成，PASS 通过率
    <span th:text="${passRate}">X</span>，<span class="highlight">详情查看附件！</span>
</p>
<p>测试报告路径: <a th:href="${reportPath}">点击这里查看测试报告</a></p>
<p>测试开始时间: <span th:text="${startTime}">X</span></p>
<p>测试结束时间: <span th:text="${endTime}">X</span></p>

<table class="table">
    <thead>
    <tr class="header">
        <th>测试指标</th>
        <th>测试结果</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>测试案例数</td>
        <td th:text="${totalCases}">X</td>
    </tr>
    <tr>
        <td>通过案例</td>
        <td th:text="${totalPass}">X</td>
    </tr>
    <tr>
        <td>不通过案例</td>
        <td th:text="${totalFail}">X</td>
    </tr>
    <tr>
        <td>通过率</td>
        <td th:text="${overallPassRate}">X</td>
    </tr>
    <tr>
        <td>测试结论</td>
        <td th:text="${conclusion}">X</td>
    </tr>
    </tbody>
</table>
</body>
</html>
