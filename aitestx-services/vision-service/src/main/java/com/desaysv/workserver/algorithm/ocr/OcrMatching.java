package com.desaysv.workserver.algorithm.ocr;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.utils.ImageUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacpp.BytePointer;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.leptonica.PIX;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Size;
import org.bytedeco.tesseract.TessBaseAPI;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static org.bytedeco.leptonica.global.leptonica.pixRead;
import static org.bytedeco.tesseract.global.tesseract.TessBaseAPICreate;
import static org.bytedeco.tesseract.global.tesseract.TessBaseAPIInit3;

/**
 * OCR图像转文字识别
 */
@Service
@Slf4j
@Lazy
public class OcrMatching extends ImageMatchingBaseTemplate {

    private final File ocrEngine;

    public OcrMatching() {
        if (SpringContextHolder.isDevEnvironment()) {
            ocrEngine = new File(Objects.requireNonNull(
                    Thread.currentThread().getClass().getResource("/lib/PaddleOCR-json_v.1.3.1/PaddleOCR-json.exe")).getPath());
        } else {
            String exePath = "PaddleOCR-json_v.1.3.1\\PaddleOCR-json.exe";
            ocrEngine = Paths.get("D:\\FlyTest\\bin\\lib", exePath).toFile();
        }
    }

    @Override
    public String getFriendlyName() {
        return "字符识别";
    }

    private static byte[] frameToBytes(Frame frame) throws IOException {
        BufferedImage bufferedImage = Java2DFrameUtils.toBufferedImage(frame);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "png", byteArrayOutputStream);
        return byteArrayOutputStream.toByteArray();
    }

    private List<OcrEntry> ocr(Frame frame) {
        Map<String, Object> arguments = new HashMap<>();
        List<OcrEntry> ocrEntries = new ArrayList<>();
        try (Ocr ocr = new Ocr(ocrEngine, arguments)) {
            OcrResponse resp = ocr.runOcrOnImgBytes(frameToBytes(frame));
            // 读取结果
            if (resp.code == OcrCode.OK) {
                OcrEntry[] data = resp.data;
//                for (OcrEntry entry : resp.data) {
//                    log.info("OCR结果:{}", entry.text);
//                }
                ocrEntries.addAll(Arrays.asList(data));
            } else {
                log.warn("ocr error: code={}, msg={}", resp.code, resp.msg);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return ocrEntries;
    }

    @Override
    protected VisionResult templateMatching(VisionAlgorithm visionAlgorithm) {
        log.info("OcrMatching:{}", visionAlgorithm);
        Frame liveFrame = visionAlgorithm.getLiveFrame();
        try (Frame cropFrame = ImageUtils.crop(liveFrame, visionAlgorithm.getRoiRect())) {
            List<OcrEntry> ocrEntries = ocr(cropFrame);
            double avgScore = ocrEntries.stream().mapToDouble(OcrEntry::getScore).average().orElse(0);
            String ocrText = ocrEntries.stream().map(entry -> entry.text).collect(Collectors.joining(" "));
            log.info("ocr识别结果:{}", ocrEntries);
            String message = String.format("ocr识别到的文字:%s，目标文字:%s", ocrText, visionAlgorithm.getMatchedText());
            log.info(message);
            double score;
            if (visionAlgorithm.isOnlyTestSimilarity()) {
                score = avgScore;
            } else {
                double diff = StrUtils.getDiff(ocrText, visionAlgorithm.getMatchedText());
                score = ocrText.contains(visionAlgorithm.getMatchedText()) ? 1 : Math.min(avgScore, diff);
            }
            VisionResult visionResult = new VisionResult();
            visionResult.setScore(score);
            visionResult.setExtraDesc(message);
            visionResult.setOcrResultText(ocrText);
            return visionResult;
        }
    }

    public static void main(String[] args) throws IOException {
        System.out.println(StrUtils.getDiff("33.0.0.0", "33.0.0.4"));
//        BufferedImage bufferedImage1 = ImageIO.read(new File("D:\\uidp4666\\My Pictures\\1.png"));
//        new OcrMatching().ocr(Java2DFrameUtils.toFrame(bufferedImage1));
    }

    public static void useTessBaseApi() {
        // 读取图片并使用JavaCV进行预处理

        String imageFile = "D:\\uidp4666\\My Pictures\\3.png"; // 你的图片路径
        String tessdata = "D:\\Program Files\\Tesseract-OCR\\tessdata"; // tessdata目录路径
        String lang = "chi_sim+eng"; // 使用的语言模型

        TessBaseAPI api = TessBaseAPICreate();
        // 初始化Tesseract-OCR，无需指定tessdata路径以及识别语言类型
        if (TessBaseAPIInit3(api, tessdata, lang) != 0) {
            System.err.println("Could not initialize Tesseract.");
            System.exit(1);
        }
        try (Mat img = opencv_imgcodecs.imread(imageFile);
             Mat gray = new Mat();
             Mat result = new Mat()) {

            // 转为灰度图像
            opencv_imgproc.cvtColor(img, gray, opencv_imgproc.COLOR_BGR2GRAY);

            // 自适应阈值处理
            opencv_imgproc.adaptiveThreshold(gray, result, 255, opencv_imgproc.ADAPTIVE_THRESH_GAUSSIAN_C, opencv_imgproc.THRESH_BINARY_INV, 35, 20);

            // 形态学操作: 开运算（膨胀后腐蚀），有助于去除小的干扰和空洞
            Mat kernel = opencv_imgproc.getStructuringElement(opencv_imgproc.MORPH_RECT, new Size(2, 2));
            opencv_imgproc.morphologyEx(result, result, opencv_imgproc.MORPH_CLOSE, kernel);


            opencv_imgcodecs.imwrite("D:\\uidp4666\\My Pictures\\processed_image.png", result);

            PIX pix = pixRead("D:\\uidp4666\\My Pictures\\processed_image.png");
            api.SetImage(pix);

            // 进行OCR识别并输出结果
            BytePointer outText = api.GetUTF8Text();
            String str = outText.getString();
            System.out.println("OCR output: \n" + str);

            // 释放内存
            api.End();
            outText.deallocate();
            pix.destroy();
        }
    }


}
