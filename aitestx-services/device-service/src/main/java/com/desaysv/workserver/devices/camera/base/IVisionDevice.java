package com.desaysv.workserver.devices.camera.base;

import com.desaysv.workserver.action_sequence.ActionSequenceConfigManager;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IVisionDevice {
    Logger log = LogManager.getLogger(IVisionDevice.class.getSimpleName());

    float DEFAULT_THRESHOLD = 0.8f;
    String DEFAULT_THRESHOLD_PERCENT = "80%";

    static String getAlgorithm() {
        String configAlgorithm = ActionSequenceConfigManager.getConfig().getImageAlgorithmConfig().getAlgorithm();
        return StrUtils.isEmpty(configAlgorithm) ? AlgorithmSets.strictTemplateMatching : configAlgorithm;
    }

    static float getRoiEnlargePercent() {
        return ActionSequenceConfigManager.getConfig().getImageAlgorithmConfig().getRoiEnlargePercent();
    }

    static float getThresholdFloat(String threshold) {
        float thresholdFloat = DEFAULT_THRESHOLD;
        threshold = threshold.trim();
        if (threshold.endsWith("%")) {
            threshold = threshold.substring(0, threshold.length() - 1);
            thresholdFloat = Float.parseFloat(threshold) / 100.0f;
        }
        return thresholdFloat;
    }

    VisionResult imageMatch(String templateName, boolean mustExist, float threshold, String matchText, String algorithm, float roiEnlargePercent);

    VisionResult imageModelMatch(String templateName, boolean mustExist, float threshold, String matchText, String algorithm, float roiEnlargePercent, String model);

    default boolean applyAlgorithm(String templateName, boolean mustExist, float threshold, String algorithm) {
        VisionResult visionResult = applyAlgorithm(templateName, mustExist, threshold, null, algorithm);
        return visionResult.isPassed();
    }

    default VisionResult applyAlgorithm(String templateName, boolean mustExist, float threshold, String matchText, String algorithm) {
        VisionResult visionResult = imageMatch(templateName, mustExist, threshold, matchText, algorithm, getRoiEnlargePercent());
        ActionSequencesLoggerUtil.info(visionResult.getMessage());
        if (visionResult.isFailed()) {
            if (visionResult.getFileVisionResult().getTemplateImagePath() != null) {
                ActionSequencesLoggerUtil.info("模板图片路径:{}", visionResult.getFileVisionResult().getTemplateImagePath());
            }
            if (visionResult.getFileVisionResult().getCaptureImagePath() != null) {
                ActionSequencesLoggerUtil.info("失败图片路径:{}", visionResult.getFileVisionResult().getCaptureImagePath());
            }
        }
        return visionResult;
    }

    default VisionResult applyModelAlgorithm(String templateName, boolean mustExist, float threshold, String matchText, String algorithm, String model) {
        VisionResult visionResult = imageModelMatch(templateName, mustExist, threshold, matchText, algorithm, getRoiEnlargePercent(), model);
        ActionSequencesLoggerUtil.info(visionResult.getMessage());
        if (visionResult.isFailed()) {
            if (visionResult.getFileVisionResult().getTemplateImagePath() != null) {
                ActionSequencesLoggerUtil.info("模板图片路径:{}", visionResult.getFileVisionResult().getTemplateImagePath());
            }
            if (visionResult.getFileVisionResult().getCaptureImagePath() != null) {
                ActionSequencesLoggerUtil.info("失败图片路径:{}", visionResult.getFileVisionResult().getCaptureImagePath());
            }
        }
        return visionResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_ALL"})
    default ActualExpectedResult patternRecognize(String templateName, String threshold, String algorithm) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
//        boolean pass = applyAlgorithm(templateName, true, thresholdFloat, algorithm);
        VisionResult visionResult = applyAlgorithm(templateName, true, thresholdFloat, null, algorithm);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("patternRecognize", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_THRESHOLD"})
    default ActualExpectedResult patternRecognize(String templateName, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyAlgorithm(templateName, true, thresholdFloat, null, getAlgorithm());
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("patternRecognize", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).MODEL_RECOGNITION_LIGHT_THRESHOLD"})
    default ActualExpectedResult modelRecognizeInLightWithThreshold(String templateName, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyModelAlgorithm(templateName, true, thresholdFloat, null, getAlgorithm(), "light");
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("modelRecognize", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).MODEL_RECOGNITION_DARK_THRESHOLD"})
    default ActualExpectedResult modelRecognizeInDarkWithThreshold(String templateName, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyModelAlgorithm(templateName, true, thresholdFloat, null, getAlgorithm(), "dark");
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("modelRecognize", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).MODEL_RECOGNITION_LIGHT"})
    default ActualExpectedResult modelRecognizeInLight(String templateName) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyModelAlgorithm(templateName, true, DEFAULT_THRESHOLD, null, getAlgorithm(), "light");
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("modelRecognize", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).MODEL_RECOGNITION_DARK"})
    default ActualExpectedResult modelRecognizeInDark(String templateName) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyModelAlgorithm(templateName, true, DEFAULT_THRESHOLD, null, getAlgorithm(), "dark");
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("modelRecognize", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_ALGORITHM"})
    default ActualExpectedResult patternRecognizeByAlgorithm(String templateName, String algorithm) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyAlgorithm(templateName, true, DEFAULT_THRESHOLD, null, algorithm);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("patternRecognizeByAlgorithm", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION"})
    default ActualExpectedResult patternRecognize(String templateName) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyAlgorithm(templateName, true, DEFAULT_THRESHOLD, null, getAlgorithm());
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("patternRecognize", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_NO_EXIST"})
    default ActualExpectedResult patternRecognizeNoExist(String templateName) {
        return patternRecognizeNoExist(templateName, DEFAULT_THRESHOLD_PERCENT);
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_NO_EXIST_THRESHOLD"})
    default ActualExpectedResult patternRecognizeNoExist(String templateName, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyAlgorithm(templateName, false, thresholdFloat, null, getAlgorithm());
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) :
                String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("patternRecognizeNoExist", visionResult.isPassed(), resultValue);
        return actualExpectedResult;

    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_CHARA_RECOGNITION_FOR_NA"})
    default ActualExpectedResult ocrRecognizeNA(String templateName, String anyString) {
        return ocrRecognize(templateName, anyString);
    }

    /**
     * 字符OCR识别（不带阈值）
     *
     * @param templateName 模板名称
     * @param anyString    匹配文本
     * @return 是否识别成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_CHAR_RECOGNITION"})
    default ActualExpectedResult ocrRecognize(String templateName, String anyString) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyAlgorithm(templateName, true, DEFAULT_THRESHOLD, anyString, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        //获取方法名
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        actualExpectedResult.put(methodName, visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    /**
     * 字符OCR识别（带阈值）
     *
     * @param templateName 模板名称
     * @param anyString    匹配文本
     * @param threshold    识别阈值
     * @return 是否识别成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_CHAR_RECOGNITION_THRESHOLD"})
    default ActualExpectedResult ocrRecognizeWithThreshold(String templateName, String anyString, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyAlgorithm(templateName, true, thresholdFloat, anyString, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("ocrRecognizeWithThreshold", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_CHAR_RECOGNITION_NOT_EXIST"})
    default ActualExpectedResult ocrRecognizeNotExist(String templateName, String anyString) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyAlgorithm(templateName, false, DEFAULT_THRESHOLD, anyString, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        //获取方法名
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        actualExpectedResult.put(methodName, visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_CHAR_RECOGNITION_THRESHOLD_NOT_EXIST"})
    default ActualExpectedResult ocrRecognizeWithThresholdNotExist(String templateName, String anyString, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyAlgorithm(templateName, false, thresholdFloat, anyString, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        //获取方法名
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        actualExpectedResult.put(methodName, visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    /**
     * 字符OCR识别（带阈值） 格式：Vision-OCR-X1(识别字符/模板名)-X2(相似度最小百分比)
     *
     * @param templateName 模板名称也是需要识别的字符
     * @param threshold    识别阈值
     * @return 是否识别成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_TEMPLATE_NAME_RECOGNITION_THRESHOLD"})
    default ActualExpectedResult ocrRecognizeCharWithThreshold(String templateName, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyAlgorithm(templateName, true, thresholdFloat, templateName, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("ocrRecognizeCharWithThreshold", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    /**
     * 字符OCR识别（不带阈值） 格式：Vision-OCR-X1(识别字符/模板名)
     *
     * @param templateName 模板名称也是需要识别的字符
     * @return 是否识别成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_TEMPLATE_NAME_RECOGNITION"})
    default ActualExpectedResult ocrRecognizeChar(String templateName) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyAlgorithm(templateName, true, DEFAULT_THRESHOLD, templateName, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("ocrRecognizeChar", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    /**
     * OCR字符识别（反向不存在，带阈值）格式：Vision-OCR-NoExist-X1(识别字符/模板名)-X2(相似度最小百分比)
     *
     * @param templateName 模板名称也是需要识别的字符
     * @param threshold    识别阈值
     * @return 是否识别成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_NO_EXIST_TEMPLATE_NAME_RECOGNITION_THRESHOLD"})
    default ActualExpectedResult ocrRecognizeCharNoExistWithThreshold(String templateName, String threshold) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = applyAlgorithm(templateName, false, thresholdFloat, templateName, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("ocrRecognizeCharNoExistWithThreshold", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    /**
     * OCR字符识别（反向不存在，不带阈值）格式：Vision-OCR-NoExist-X1(识别字符/模板名)
     *
     * @param templateName 模板名称也是需要识别的字符
     * @return 是否识别成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_NO_EXIST_TEMPLATE_NAME_RECOGNITION"})
    default ActualExpectedResult ocrRecognizeCharNoExist(String templateName) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        VisionResult visionResult = applyAlgorithm(templateName, false, DEFAULT_THRESHOLD, templateName, AlgorithmSets.ocrMatching);
        String resultValue = visionResult.isPassed() ? String.format("相似度：%s", visionResult.getPercentScore()) : String.format("相似度：%s, 失败图片路径：%s", visionResult.getPercentScore(), visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("ocrRecognizeCharNoExist", visionResult.isPassed(), resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_NUMBER_RECOGNITION"})
    default boolean ocrNumberRecognize() {
        return true;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).OCR_TIME_RECOGNITION"})
    default boolean ocrTimeRecognize() {
        return true;
    }

    /**
     * 闪烁识别
     *
     * @param templateName 模板名
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_LIGHT_BLINKING"})
    default ActualExpectedResult patternRecognizeLightBlinking(String templateName) {
        return patternRecognizeLightBlinkingAlgorithm(templateName, 0.0f, 0.0f, "80%", "80%", null);
    }

    /**
     * 闪烁识别
     *
     * @param templateName 模板名
     * @param startValue   频率起始
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_LIGHT_BLINKING_START_VALUE"})
    default ActualExpectedResult patternRecognizeLightBlinkingStartValue(String templateName, float startValue) {
        return patternRecognizeLightBlinkingAlgorithm(templateName, startValue, 0.0f, "80%", "80%", null);
    }

    /**
     * 闪烁识别
     *
     * @param templateName 模板名
     * @param startValue   频率起始
     * @param endValue     频率终止
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_LIGHT_BLINKING_ALL"})
    default ActualExpectedResult patternRecognizeLightBlinkingAll(String templateName, float startValue, float endValue) {
        return patternRecognizeLightBlinkingAlgorithm(templateName, startValue, endValue, "80%", "80%", null);
    }

    /**
     * 闪烁识别
     *
     * @param templateName     模板名
     * @param startValue       频率起始
     * @param endValue         频率终止
     * @param targetSimilarity 模板与图像相似率
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_LIGHT_BLINKING_RATIO"})
    default ActualExpectedResult patternRecognizeLightBlinkingRatio(String templateName, float startValue, float endValue, String targetSimilarity) {
        return patternRecognizeLightBlinkingAlgorithm(templateName, startValue, endValue, "80%", targetSimilarity, null);
    }

    /**
     * 闪烁识别
     *
     * @param templateName     模板名
     * @param startValue       频率起始
     * @param endValue         频率终止
     * @param threshold        亮与不亮阈值(默认0.80)
     * @param targetSimilarity 模板与图像相似率
     * @param algorithm        算法
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).PATTERN_RECOGNITION_LIGHT_BLINKING_ALGORITHM"})
    default ActualExpectedResult patternRecognizeLightBlinkingAlgorithm(String templateName, float startValue, float endValue, String threshold, String targetSimilarity, String algorithm) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float targetSimilarityFloat = getThresholdFloat(targetSimilarity);
        float thresholdFloat = getThresholdFloat(threshold);
        VisionResult visionResult = videoMatch(templateName, 5000, thresholdFloat, targetSimilarityFloat, algorithm);
        // 判断 visionResult.getFrequency() 是否在 startValue 和 endValue 范围内
        boolean pass = false;

        if (visionResult.isPassed()) {
            double frequency = visionResult.getFrequency();
            if (startValue == 0.0f && endValue == 0.0f) {
                log.info("频率不计算，检测通过。");
                pass = true;
            } else if (endValue == 0.0f) {
                if (frequency >= startValue) {
                    log.info(String.format("频率 %f Hz 大于等于 %f，检测通过。", frequency, startValue));
                    pass = true;
                }
            } else {
                if (frequency >= startValue && frequency <= endValue) {
                    // 频率在指定范围内，检测通过
                    log.info(String.format("频率 %f Hz 在范围 [%f, %f] 内，检测通过。", frequency, startValue, endValue));
                    pass = true;
                } else {
                    // 频率不在指定范围内，检测失败
                    log.info(String.format("频率 %f Hz 不在范围 [%f, %f] 内，检测失败。", frequency, startValue, endValue));
                }
            }

        } else {
            log.info(visionResult.getMessage() == null ? "检测未通过，未找到闪烁。" : visionResult.getMessage());
        }
        String resultValue = visionResult.isPassed() ? String.format("闪烁频率：%s", visionResult.getFrequency()) : String.format("未发现闪烁, 失败图片路径：%s", visionResult.getFileVisionResult().getCaptureImagePath());
        actualExpectedResult.put("patternRecognizeLightBlinkingAlgorithm", pass, resultValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).TAKE_PHOTO"})
    default OperationResult takePhoto() {
        return new OperationResult().ok();
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.VisionRegexRule).VIDEO_RECORDING"})
    default OperationResult videoRecording(String status) {
        return new OperationResult().ok();
    }


    VisionResult videoMatch(String templateName, double recognizedDuration, float threshold, float targetSimilarity, String algorithm);

    static void main(String[] args) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        actualExpectedResult.put("patternRecognize", true, 123);
        System.out.println("actualExpectedResult: " + actualExpectedResult);
    }

}
