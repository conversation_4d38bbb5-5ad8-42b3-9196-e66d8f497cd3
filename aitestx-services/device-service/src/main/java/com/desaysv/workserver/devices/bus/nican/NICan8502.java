package com.desaysv.workserver.devices.bus.nican;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class NICan8502 extends NICan {
    public NICan8502() {
        this(new DeviceOperationParameter());
    }

    public NICan8502(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public int getMaxChannelCount() {
        return 2;
    }
    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.NI_CAN_8502;
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        return false;
    }
}
