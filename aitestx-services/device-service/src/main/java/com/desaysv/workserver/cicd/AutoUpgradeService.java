package com.desaysv.workserver.cicd;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.common.port.MessageText;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.devices.usbswtich.UsbSwitchDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.entity.UpgradeResultReportDto;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.manager.DeviceManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sun.jna.platform.win32.Kernel32;
import com.sun.jna.platform.win32.WinBase;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AutoUpgradeService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private final DeviceManager deviceManager; // 确保DeviceManager被注入

    @Autowired
    public AutoUpgradeService(DeviceManager deviceManager) {
        this.deviceManager = deviceManager;
    }
    /**
     * 获取所有USB切换板
     *
     * @return 所有USB切换板列表
     */
    public List<Device> listUsbSwitches() {
        log.info("正在获取所有USB切换板");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> usbSwitches = devices.stream() // 使用流来过滤设备
                    .filter(device -> "usbSwitchType".equals(device.getDeviceType())) // 筛选出deviceType为usbSwitchType的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了 {} 个USB切换板,分别是：{}", usbSwitches.size(), usbSwitches);
            return usbSwitches;
        }catch (Exception e) {
            log.error("获取USB切换板失败", e);
            throw new RuntimeException("获取USB切换板失败", e);
        }
    }

    /**
     * 获取所有串口
     * @return 所有串口列表
     * */
    public List<Device> listSerialPorts() {
        log.info("正在获取所有串口");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> serialPorts = devices.stream() // 使用流来过滤设备
                    .filter(device -> "serialType".equals(device.getDeviceType())) // 筛选出deviceType为serialPort的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了{}个串口，分别是：{}", serialPorts.size(), serialPorts);
            return serialPorts;
        }catch (Exception e){
            log.error("获取串口失败", e);
            throw new RuntimeException("获取串口失败", e);
        }
    }

    /**
     * 打开USB切换板
     *
     * @param channel 通道编号
     * @return 是否成功打开USB切换板
     */
    public boolean openUsbSwitches(List<Device> usbSwitches,int channel) {
        boolean success = true;
        try{
            for (Device usbSwitch : usbSwitches){
                log.info("打开USB切换板: {}", usbSwitch);
                //把Device usbSwitch转换为UsbSwitchDevice类型
                UsbSwitchDevice usbSwitchDevice = (UsbSwitchDevice) usbSwitch;
                switch (channel) {
                    case 1:
                        usbSwitchDevice.open1();
                        break;
                    case 2:
                        usbSwitchDevice.open2();
                        break;
                    case 3:
                        usbSwitchDevice.open3();
                        break;
                    case 4:
                        usbSwitchDevice.open4();
                        break;
                    case 5:
                        usbSwitchDevice.open5();
                        break;
                    default:
                        log.error("无效的通道编号: {}", channel);
                        success = false;
                }
            }
        }catch (DeviceSendException e){
            log.error("USB切换板打开失败", e);
            throw new RuntimeException("USB切换板打开失败", e);
        }
        return success;
    }

    /**
     * 发送消息到串口
     *
     * @param serialPorts 串口列表
     * @param command 要发送的消息
     * @return 是否成功发送消息
     */
    public boolean sendToSerialPort(List<Device> serialPorts,String command){
        try{
            for (Device serialPort : serialPorts) {
                log.info("发送消息到串口: {}", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                return serialPortDevice.send(command,false);
            }
        }catch (DeviceSendException e){
            log.error("发送消息到串口失败", e);
        }
        return false;
    }

    /**
     * 向串口发送消息并接收返回数据
     *
     * @param serialPorts 串口列表
     * @param command 要发送的消息
     * @param checkedContext 要检查的返回数据
     * @return 返回数据
     */
    public String sendAndReceiveToSerialPort(List<Device> serialPorts,String command,String checkedContext)  {
        try{
            for (Device serialPort : serialPorts) {
                log.info("向串口: {}发送消息", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                MessageText  messageText = new MessageText();
                messageText.setSendText(command);
                messageText.setMatchText(checkedContext);
                messageText.setHex(false);
                return serialPortDevice.sendAndMatchReturnData(messageText);
            }
        }catch (OperationFailNotification e){
            log.error("发送消息到串口失败", e);
        }
        return null;
    }

    public boolean monitorStartSerialPort(List<Device> serialPorts,String checkedContext) {
        try{
            for (Device serialPort : serialPorts) {
                log.info("开始监控串口: {}", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                return serialPortDevice.mustExistMonitorStart(checkedContext);
            }
        }catch (Exception e){
            log.error("监控串口失败", e);
        }
        return false;
    }

    public boolean monitorEndSerialPort(List<Device> serialPorts) {
        try{
            for (Device serialPort : serialPorts) {
                log.info("结束监控串口: {}", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                return serialPortDevice.mustExistMonitorEnd().isOk();
            }
        }catch (Exception e){
            log.error("结束监控串口失败", e);
        }
        return false;
    }



    /**
     * 查找U盘路径
     *
     * @return U盘路径列表
     */
    public List<String> findUDiskPath() {
        List<String> uDiskPaths = new ArrayList<>();
        log.info("正在查找U盘路径");
        for (char driveLetter = 'A'; driveLetter <= 'Z'; driveLetter++) {
            String rootPath = driveLetter + ":\\";

            IntByReference volumeSerialNumber = new IntByReference(0);
            IntByReference maximumComponentLength = new IntByReference(0);
            IntByReference fileSystemFlags = new IntByReference(0);

            char[] fileSystemNameBuffer = new char[1024];

            boolean result = Kernel32.INSTANCE.GetVolumeInformation(
                    rootPath,
                    null, 0,
                    volumeSerialNumber,
                    maximumComponentLength,
                    fileSystemFlags,
                    fileSystemNameBuffer,
                    fileSystemNameBuffer.length);
            // 增加有效性判断
            if (!result || fileSystemNameBuffer[0] == 0) {
//                log.debug("跳过无效驱动器: {}", rootPath);
                continue;
            }
            int driveType = Kernel32.INSTANCE.GetDriveType(rootPath);
            if (driveType == WinBase.DRIVE_REMOVABLE) {
                uDiskPaths.add(rootPath);
                log.info("找到U盘的路径为: {}", rootPath);
            }
        }
        return uDiskPaths;
    }

    /**
     * 拷贝本地文件
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @throws IOException
     */
    public static void copyLocalFile(Path sourcePath, Path targetPath) throws IOException {
        try {
            // 执行拷贝（自动覆盖已存在文件）
            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件拷贝成功，源文件路径：{}，目标文件路径：{}", sourcePath, targetPath);
        } catch (AccessDeniedException e) {
            log.error("权限不足，请以管理员身份运行程序或检查U盘写保护状态");
        } catch (FileSystemException e) {
            log.error("文件系统错误: {}", e.getReason());
        }
    }

    /**
     * 拷贝大文件（支持网络路径和本地路径）
     *
     * @param sourceFilePath 源文件完整路径（包含文件名）
     * @param targetDirPath  目标目录路径
     * @throws IOException 当拷贝过程中发生错误时抛出
     */
    public static void copyLargeFile(Path sourceFilePath, Path targetDirPath) throws IOException {
        File sourceFile = sourceFilePath.toFile();
        String fileName = sourceFile.getName();

        log.info("安装包文件路径: {}", sourceFile.getAbsolutePath());
        log.info("目标目录路径: {}", targetDirPath);

        // 获取源文件大小（字节）
        long sourceFileSizeBytes = sourceFile.length();
        double sourceFileSizeGB = sourceFileSizeBytes / 1024.0 / 1024.0 / 1024.0;
        String formattedFileSize = String.format("%.2f", sourceFileSizeGB);
        log.info("文件大小: {} GB", formattedFileSize);

        // 获取目标磁盘信息
        Path targetDrive = targetDirPath.getRoot();
        FileStore store = Files.getFileStore(targetDrive);

        // 1. 检查磁盘空间
        long availableSpace = store.getUsableSpace();
        double availableSpaceGB = availableSpace / (1024.0 * 1024 * 1024);
        log.info("需要空间: {} GB, 可用空间: {} GB", formattedFileSize, String.format("%.2f", availableSpaceGB));

        // 增加10%的安全缓冲空间
        if (availableSpace < sourceFileSizeBytes * 1.1) {
            double requiredExtraGB = (sourceFileSizeBytes * 1.1 - availableSpace) / (1024.0 * 1024 * 1024);
            log.error("磁盘空间不足! 需要至少{}GB 额外空间",  String.format("%.2f", requiredExtraGB));
            throw new IOException(String.format("磁盘空间不足! 需要至少 %.2f GB 额外空间", requiredExtraGB));
        }

        // 2. 检查文件系统类型
        String fileSystemType = store.type();
        log.info("目标磁盘文件系统: {}", fileSystemType);
        if ("FAT32".equalsIgnoreCase(fileSystemType) && sourceFileSizeBytes > 4L * 1024 * 1024 * 1024) {
            log.error("FAT32文件系统不支持大于4GB的文件，请转换为NTFS");
            throw new IOException("FAT32文件系统不支持大于4GB的文件，请转换为NTFS\n转换命令: convert "
                    + targetDrive + " /FS:NTFS");
        }

        // 3. 确保目标目录存在
        if (!Files.exists(targetDirPath)) {
            Files.createDirectories(targetDirPath);
        }
        File targetFile = new File(targetDirPath.toFile(), fileName);

        // 删除已存在的零字节文件（如果存在）
        if (targetFile.exists() && targetFile.length() == 0) {
            log.warn("发现零字节的旧目标文件，正在删除: {}", targetFile.getAbsolutePath());
            if (!targetFile.delete()) {
                log.error("无法删除零字节文件: {}", targetFile.getAbsolutePath());
            }
        }

        // 4. 使用FileChannel进行高效传输
        try (FileInputStream fis = new FileInputStream(sourceFile);
             FileChannel inChannel = fis.getChannel();
             FileOutputStream fos = new FileOutputStream(targetFile);
             FileChannel outChannel = fos.getChannel()) {

            long fileSize = inChannel.size();
            long position = 0;
            final long startTime = System.currentTimeMillis();

            // 进度报告设置
            final long reportInterval = 100 * 1024 * 1024; // 每100MB报告一次
            long nextReport = reportInterval;
            final long maxChunk = 16 * 1024 * 1024; // 16MB传输块

            while (position < fileSize) {
                // 动态计算传输块大小
                long chunk = Math.min(maxChunk, fileSize - position);

                // 核心传输方法
                long transferred = inChannel.transferTo(position, chunk, outChannel);

                // 处理零传输情况
                if (transferred == 0) {
                    // 添加传输失败计数器
                    int zeroTransferCount = 0;
                    while (transferred == 0 && zeroTransferCount < 10) {
                        Thread.sleep(100);
                        transferred = inChannel.transferTo(position, chunk, outChannel);
                        zeroTransferCount++;
                    }
                    if (transferred == 0) {
                        throw new IOException("连续传输失败，位置: " + position + "/" + fileSize);
                    }
                }

                position += transferred;

                // 进度报告
                if (position >= nextReport || position == fileSize) {
                    double percent = (double) position / fileSize * 100;
                    double elapsedSec = (System.currentTimeMillis() - startTime) / 1000.0;
                    double speedMBs = position / (elapsedSec * 1024 * 1024);

                    log.info("拷贝进度: {}% ({}/{}) - 速度: {} MB/s",
                            String.format("%.2f", percent),
                            formatSize(position),
                            formatSize(fileSize),
                            String.format("%.2f", speedMBs));

                    nextReport = position + reportInterval;
                }
            }

            // 强制刷新文件系统缓存
            outChannel.force(true);
            fos.getFD().sync();

            validateFileCopy(sourceFile, targetFile); // 校验文件完整性

            // 拷贝完成统计
            double totalTimeSec = (System.currentTimeMillis() - startTime) / 1000.0;
            double avgSpeedMBs = fileSize / (totalTimeSec * 1024 * 1024);
            log.info("文件拷贝成功! 总耗时: {}秒, 平均速度: {} MB/s",
                    String.format("%.2f", totalTimeSec),
                    String.format("%.2f", avgSpeedMBs));

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            throw new IOException("文件拷贝被中断", e);
        }
    }

    /**
     * 校验文件拷贝完整性
     */
    private static void validateFileCopy(File sourceFile, File targetFile) throws IOException {
        long sourceSize = sourceFile.length();
        long targetSize = targetFile.length();

        // 等待文件系统更新（处理延迟）
        int retryCount = 0;
        while (targetSize == 0 && retryCount < 5) {
            log.warn("检测到零字节文件，等待文件系统更新... (重试 {}/{})", retryCount + 1, 5);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            targetSize = targetFile.length(); // 重新获取大小
            retryCount++;
        }

        if (targetSize == 0) {
            log.error("目标文件大小为0字节! 源文件: {} ({}字节), 目标文件: {}",
                    sourceFile.getAbsolutePath(), sourceSize,
                    targetFile.getAbsolutePath());

            // 尝试删除无效文件
            if (targetFile.exists() && !targetFile.delete()) {
                log.error("无法删除无效的目标文件");
            }

            throw new IOException("文件拷贝失败: 目标文件大小为0字节");
        }

        if (targetSize != sourceSize) {
            log.error("文件大小不匹配! 源文件: {} ({}字节), 目标文件: {} ({}字节), 差异: {}字节",
                    sourceFile.getAbsolutePath(), sourceSize,
                    targetFile.getAbsolutePath(), targetSize,
                    Math.abs(sourceSize - targetSize));

            throw new IOException("文件拷贝不完整: 目标文件大小(" + targetSize +
                    "字节)不等于源文件大小(" + sourceSize + "字节)");
        }

        log.info("文件校验成功: 源文件大小={}字节, 目标文件大小={}字节", sourceSize, targetSize);
    }

    /**
     * 清空目录内容（保留目录本身）
     */
    private static void deleteDirectoryContents(Path dir) throws IOException {
        if (Files.exists(dir)) {
            Files.walk(dir)
                    .sorted(Comparator.reverseOrder())
                    .forEach(path -> {
                        try {
                            if (!path.equals(dir)) { // 不删除目录本身
                                Files.deleteIfExists(path);
                            }
                        } catch (IOException e) {
                            log.warn("删除文件失败：{}", path, e);
                        }
                    });
        }
    }

    /**
     * 从python进程获取构建信息
     */
    public BuildInfoResponse fetchBuildInfo() {
        log.info("正在获取构建信息");
        try {
            URL url = new URL("http://localhost:5000/get_artifact_url");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");

            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("获取到构建信息：{}", response);

                String json = response.toString(); // 从 fetchArtifacts() 方法中获取的响应
                ObjectMapper objectMapper = new ObjectMapper();
                BuildInfoResponse buildInfoResponse = objectMapper.readValue(json, BuildInfoResponse.class);
                log.info("解析构建信息成功！：{}", buildInfoResponse);
                return buildInfoResponse;
            } else {
                log.error("获取构建信息失败，状态码：{}", responseCode);
                if (responseCode == 403){
                    log.error("获取构建信息接口访问已达3次上限");
                    upgradeResultReport(new UpgradeResultReportDto("", "NA", "", "", "接口访问已达3次上限,轮询已被重置，构建信息已清空，无法获取！！"));
                    try {
                        uploadUpgradeResult("{\"result\":\"升级失败，因为接口访问已达3次上限,轮询已被重置，构建信息已清空，无法获取！！\"}");
                    }catch (Exception e){
                        log.error("上传升级结果失败", e);
                    }
                }
                if (responseCode == 404){
                    log.error("获取构建信息接口访问失败");
                    upgradeResultReport(new UpgradeResultReportDto("", "NA", "", "", "无测试任务，获取构建信息接口不可达，失败！！"));
                    try {
                        uploadUpgradeResult("{\"result\":\"升级失败，因为无测试任务，获取构建信息接口不可达，失败！！\"}");
                    }catch (Exception e){
                        log.error("上传升级结果失败", e);
                    }
                }
            }
        }catch (Exception e){
            log.error("获取构建信息失败", e);
        }
        return null;
    }


    public UpgradeResponse autoUpgrade() {
        UpgradeResponse upgradeResponse = new UpgradeResponse();
        //1.获取构建信息
        BuildInfoResponse buildInfoResponse = fetchBuildInfo();
        if (buildInfoResponse == null) {
            upgradeResponse.setErrorMessage("获取构建信息失败");
            return upgradeResponse;
        }
        CiBuildInfo buildInfo = buildInfoResponse.getCiBuildInfo();

        //2. 获取所有USB切换板
        List<Device> usbSwitches = listUsbSwitches();
        if (usbSwitches.isEmpty()) {
            log.error("未找到可用的USB切换板设备");
            upgradeResponse.setErrorMessage("未找到可用的USB切换板设备");
            upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "未找到可用的USB切换板设备"));
            try {
                uploadUpgradeResult("{\"result\":\"升级失败，因为未找到可用的USB切换板设备！\"}");
            }catch (Exception e){
                log.error("上传升级结果失败", e);
            }
            return upgradeResponse;
        }

        try {
            // 3. 打开通道5 切到电脑
            if (!openUsbSwitches(usbSwitches, 5)) {
                log.error("打开USB切换板通道5失败");
                upgradeResponse.setErrorMessage("打开USB切换板通道5失败");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "打开USB切换板通道5失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为打开USB切换板通道5失败！\"}");
                }catch (Exception e){
                    log.error("上传升级结果失败", e);
                }
                return upgradeResponse;
            }
            log.info("成功打开USB切换板通道5");
            //等待5秒
            Thread.sleep(5000);

            // 4. 获取U盘路径（取第一个检测到的U盘）
            List<String> uDiskPaths = findUDiskPath();
            if (uDiskPaths.isEmpty()) {
                log.error("未检测到可用U盘");
                upgradeResponse.setErrorMessage("未检测到可用U盘");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "未检测到可用U盘"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为未检测到可用U盘！\"}");
                }catch (Exception e){
                    log.error("上传升级结果失败", e);
                }
                return upgradeResponse;
            }
            String uDiskPath = uDiskPaths.get(0);
            log.info("检测到U盘路径: {}", uDiskPath);

            // 5. 拷贝升级文件到U盘
            //projectId格式为：E01-IC4565，提取-后面内容
            // 正则表达式：匹配 "-" 后的内容
            String targetDirName = "";
            String regex = "-([^\\s-]+)$";  // 匹配最后一个 '-' 后的所有字符直到字符串结束
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(buildInfoResponse.getCiBuildInfo().getProjectId());
            if (matcher.find()) {
                targetDirName = matcher.group(1);  // 取出第一个分组的内容
                log.info("提取projectId中“-”后的内容: {}", targetDirName);
            } else {
                log.info("未找到匹配内容");
                upgradeResponse.setErrorMessage("提取projectId中“-”后的内容失败,projectId为："+buildInfoResponse.getCiBuildInfo().getProjectId());
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "提取projectId中“-”后的内容失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为提取projectId中“-”后的内容失败！\"}");
                }catch (Exception e){
                    log.error("上传升级结果失败", e);
                }
                return upgradeResponse;
            }
            Path targetDir = Paths.get(uDiskPath, targetDirName);
            log.info("目标文件夹路径: {}", targetDir);
            try{
                if (!Files.exists(targetDir)) {
                    Files.createDirectories(targetDir);
                    log.info("创建文件夹成功: {}", targetDir);
                }else {
                    log.info("文件夹已存在: {}", targetDir);
                    deleteDirectoryContents(targetDir);
                }
            }catch (IOException e){
                log.error("创建文件夹{}失败: {}", targetDir ,e.getMessage());
                upgradeResponse.setErrorMessage("U盘下创建文件夹失败: " + e.getMessage());
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "创建文件夹失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为创建文件夹失败！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            String dsvInnerPath = buildInfoResponse.getCiBuildInfo().getDsvInnerPath();
            String name = buildInfoResponse.getCiBuildInfo().getName();
            Path sourceFile = Paths.get(dsvInnerPath, name).normalize();
            Path fileName = sourceFile.getFileName();
            try {
//                copyLocalFile(sourceFile, targetDir.resolve(fileName));
                copyLargeFile(sourceFile, targetDir);
            } catch (IOException e) {
                log.error("安装包文件拷贝失败: {}", e.getMessage());
                upgradeResponse.setErrorMessage("安装包文件拷贝失败: " + e.getMessage());
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "安装包文件拷贝失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为安装包文件拷贝失败！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            //等待5秒
            Thread.sleep(5000);

            // 6. 切换至通道4 车机
            if (!openUsbSwitches(usbSwitches, 4)) {
                log.error("打开USB切换板通道4失败");
                upgradeResponse.setErrorMessage("打开USB切换板通道4失败");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "打开USB切换板通道4失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为打开USB切换板通道4失败！\"}");
                }catch (Exception e){
                    log.error("上传升级结果失败", e);
                }
                return upgradeResponse;
            }
            log.info("成功切换至USB切换板通道4");
            //等待5秒
            Thread.sleep(5000);

            //7. 获取所有串口
            List<Device> portDevices = listSerialPorts();
            if (portDevices.isEmpty()) {
                log.error("未找到可用的串口设备");
                upgradeResponse.setErrorMessage("未找到可用的串口设备");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "未找到可用的串口设备"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为未找到可用的串口设备！\"}");
                }catch (Exception e){
                    log.error("上传升级结果失败", e);
                }
                return upgradeResponse;
            }

            monitorStartSerialPort(portDevices,"start service success");

            //  8. 等待18分钟，等待升级完成
            try {
                Thread.sleep(16 * 60 * 1000);
                log.info("为确保升级完全完成，请耐心再等待3分钟...");
                Thread.sleep(2 * 60 * 1000);
            } catch (InterruptedException e) {
                log.error("等待升级失败: {}", e.getMessage());
                upgradeResponse.setErrorMessage("等待升级失败: " + e.getMessage());
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "等待升级失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为等待升级失败！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            if (monitorEndSerialPort(portDevices)){
                log.info("安装包安装成功");
            }else {
                log.info("安装包安装失败");
                upgradeResponse.setErrorMessage("安装包安装失败");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "安装包安装失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为安装包安装失败！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            // 9. 发送版本信息查询命令并保存结果，需要获取的有QNX、SOC、MCU版本信息，命令分别是QNX：cat /etc/version_extra; 1dtach -a /tmp/android 切安卓 2.1 MCU: getprop sys.vehicle.mcu.version 2.2 SOC: getprop ro.build.display.id
            // 并且进行对比版本信息，如果一致则升级成功，不一致则升级失败
            upgradeResponse.setVersionInfo(buildInfo.getModuleId());
            String qnxVersionInfo = sendAndReceiveToSerialPort(portDevices, "cat /etc/version_extra", buildInfo.getQnxVersion());
            if (qnxVersionInfo == null|| qnxVersionInfo.isEmpty()){
                log.error("获取QNX版本信息失败");
                upgradeResponse.setErrorMessage("获取QNX版本信息失败");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "获取QNX版本信息失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为获取QNX版本信息失败！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            if(!qnxVersionInfo.contains(buildInfo.getQnxVersion())){
                log.error("QNX版本与预期版本不一致,升级失败，实际的QNX版本信息为：{}",  qnxVersionInfo);
                upgradeResponse.setErrorMessage("QNX版本与预期版本不一致,升级失败，实际的QNX版本信息为：" + qnxVersionInfo);
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "QNX版本与预期版本不一致,升级失败，实际的QNX版本信息为：" + qnxVersionInfo));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为QNX版本与预期版本不一致！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            log.info("获取到的QNX版本信息为: {}", qnxVersionInfo);
            upgradeResponse.setQnxVersionInfo(qnxVersionInfo);

            sendAndReceiveToSerialPort(portDevices, "dtach -a /tmp/android","");
            sendAndReceiveToSerialPort(portDevices, "su","");
            String mcuVersionInfo = sendAndReceiveToSerialPort(portDevices, "getprop sys.vehicle.mcu.version", buildInfo.getMcuVersion());
            if (mcuVersionInfo == null|| mcuVersionInfo.isEmpty()){
                log.error("获取MCU版本信息失败, ");
                upgradeResponse.setErrorMessage("获取MCU版本信息失败");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "获取MCU版本信息失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为获取MCU版本信息失败！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            if(!mcuVersionInfo.contains(buildInfo.getMcuVersion())){
                log.error("MCU版本与预期版本不一致,升级失败，实际的MCU版本信息为：{}",  mcuVersionInfo);
                upgradeResponse.setErrorMessage("MCU版本与预期版本不一致,升级失败，实际的MCU版本信息为：" + mcuVersionInfo);
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "MCU版本与预期版本不一致,升级失败，实际的MCU版本信息为：" + mcuVersionInfo));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为MCU版本与预期版本不一致！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            log.info("获取到的MCU版本信息为: {}", mcuVersionInfo);
            upgradeResponse.setMcuVersionInfo(mcuVersionInfo);

            String socVersionInfo = sendAndReceiveToSerialPort(portDevices, "getprop ro.build.display.id", buildInfo.getSocVersion());
            if (socVersionInfo == null|| socVersionInfo.isEmpty()){
                log.error("获取SOC版本信息失败");
                upgradeResponse.setErrorMessage("获取SOC版本信息失败");
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "获取SOC版本信息失败"));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为获取SOC版本信息失败！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            if(!socVersionInfo.contains(buildInfo.getSocVersion())){
                log.error("SOC版本与预期版本不一致,升级失败，实际的SOC版本信息为：{}",  socVersionInfo);
                upgradeResponse.setErrorMessage("SOC版本与预期版本不一致,升级失败，实际的SOC版本信息为：" + socVersionInfo);
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "SOC版本与预期版本不一致,升级失败，实际的SOC版本信息为：" + socVersionInfo));
                try {
                    uploadUpgradeResult("{\"result\":\"升级失败，因为SOC版本与预期版本不一致！\"}");
                }catch (Exception e1){
                    log.error("上传升级结果失败", e1);
                }
                return upgradeResponse;
            }
            log.info("获取到的SOC版本信息为: {}", socVersionInfo);
            upgradeResponse.setSocVersionInfo(socVersionInfo);

            log.info("版本信息与预期版本信息一致，升级成功!!");
            upgradeResponse.setSuccess(true);
            try {
                uploadUpgradeResult("{\"result\":\"升级成功！\"}");
            }catch (Exception e1){
                log.error("上传升级结果失败", e1);
            }
        }
        catch (Exception e) {
            log.error("未知错误: {}", e.getMessage());
            upgradeResponse.setErrorMessage("未知错误: " + e.getMessage());
            if(buildInfo.getModuleId()==null){
                upgradeResultReport( new UpgradeResultReportDto("", "NA", "", "", "未知错误: " + e.getMessage()));
            }else {
                upgradeResultReport(new UpgradeResultReportDto("", buildInfo.getModuleId(), "", "", "未知错误: " + e.getMessage()));
            }
        }finally {
            try {
                // 9. 切换至通道5 切回电脑
                if (!openUsbSwitches(usbSwitches, 5)) {
                    log.error("打开USB切换板通道5失败");
                }
                log.info("成功切换至USB切换板通道5");
            }catch (Exception e){
                log.error("打开USB切换板通道5,切换到电脑失败");
            }
        }
        return upgradeResponse;
    }

    public void uploadResult(String resultJson) throws Exception {
        URL url = new URL("http://localhost:5000/receive_test_result");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; utf-8");
        conn.setDoOutput(true);

        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = resultJson.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        int code = conn.getResponseCode();
        if (code == 200) {
            System.out.println("测试结果上传成功！");
        } else {
            System.out.println("上传失败，HTTP状态码：" + code);
        }
    }

    public static void  main(String[] args) throws Exception {
//        findUDiskPath();
//        uploadResult("{\"result\":\"success\"}");
        String dsvInnerPath = "\\\\*************\\DIDA2001\\Chery\\8255\\E01\\chery.D_2025_05_28_13_49_00_1201\\02_在线软件";
        String name = "G9SH_CHERY_CHN_E01_FULL_1569_250528.zip";
        File sourceFile = new File(dsvInnerPath, name);  // 使用File处理UNC路径
        Path targetDir = Paths.get("E:\\IC4565");
        copyLargeFile(sourceFile.toPath(), targetDir);
    }

    // 辅助方法：格式化文件大小显示
    private static String formatSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        char unit = "KMGTPE".charAt(exp-1);
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), unit);
    }

    /**
     * 上传升级结果
     *
     * @param resultJson 升级结果JSON
     * @throws Exception 异常
     */
    public static void uploadUpgradeResult(String resultJson) throws Exception {
        URL url = new URL("http://localhost:5000/receive_upgrade_result");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; utf-8");
        conn.setDoOutput(true);

        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = resultJson.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        int code = conn.getResponseCode();
        if (code == 200) {
            log.info("升级结果上传到cicd_tool成功！");
        } else {
            log.error("升级结果上传到cicd_tool失败，HTTP状态码：{}", code);
        }
    }

    public boolean upgradeResultReportEmail(UpgradeResultReportDto dto) {
        String url = "http://127.0.0.1:12399/AITestX/actionSequence/mailUpgradeResult";

        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.info("cicd流水线自动升级结果邮件发送请求成功");
                return true;
            } else {
                log.error("cicd流水线自动升级结果邮件发送失败，HTTP状态码: {}", responseEntity.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.error("cicd流水线自动升级结果通知，调用邮件接口失败", e);
            return false;
        }
    }

    public boolean upgradeResultReportRobot(UpgradeResultReportDto dto) {
        String url = "http://127.0.0.1:12399/AITestX/actionSequence/robotUpgradeResult";

        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.info("cicd流水线自动升级结果机器人发送请求成功");
                return true;
            } else {
                log.error("cicd流水线自动升级结果机器人发送失败，HTTP状态码: {}", responseEntity.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.error("cicd流水线自动升级结果通知调用机器人接口失败", e);
            return false;
        }
    }

    public void upgradeResultReport(UpgradeResultReportDto dto) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        dto.setEndTime(formatter.format(new java.util.Date()));
        log.info("cicd流水线自动升级结果通知邮件和机器人！");
        upgradeResultReportEmail(dto);
        upgradeResultReportRobot(dto);
    }

}
