package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.FilterCanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

/**
 * ZCAN_USBCANFD_Mini_I底层接口
 */
public class ZlgCanFdMiniI extends ZlgCan {

    private final ZlgCanAbstractionLayer can;

    public ZlgCanFdMiniI(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        can = new ZlgCanAbstractionLayer();
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.ZLG_USBCAN_I_MINI;
    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {
        boolean isSendOk;
        if (timeout != null) {
            //阻塞发送
            isSendOk = can.blockingSend(message, timeout);
        } else {
            //正常发送
            isSendOk = can.send(getChannelHandle(message.getChannel()), message);
        }

        if (!isSendOk) {
            throw new BusError(String.format("could not send message:%s", message));
        }

    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }


    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) {
        return false;
    }

    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        return false;
    }
}
