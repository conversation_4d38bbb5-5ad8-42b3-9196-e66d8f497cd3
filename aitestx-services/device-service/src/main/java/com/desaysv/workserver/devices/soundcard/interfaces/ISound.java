package com.desaysv.workserver.devices.soundcard.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.soundcard.SoundMonitor;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 声卡操作接口
 */
public interface ISound {
    Logger log = LogManager.getLogger(ISound.class.getSimpleName());
    AtomicLong startTime = new AtomicLong(0);

    /**
     * 根据命令（"ON" 或 "OFF"）控制音频设备通道的录音状态（开启或关闭）
     *
     * @param deviceChannel
     * @param command
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_RECORD_ON_OFF"})
    default ActualExpectedResult soundRecordOnOff(Integer deviceChannel, String command) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean isOpen = command.equalsIgnoreCase("ON");
        if (isOpen) {
            getRecordedVolumes(deviceChannel).clear();
            startRecording(deviceChannel);
            startTime.set(System.currentTimeMillis());
            log.info("声卡通道{} 开始聆听", deviceChannel);
        } else {
            long executionTime = System.currentTimeMillis() - startTime.get(); // 计算耗时
            log.info("声卡通道{} 结束聆听 耗时：{}ms", deviceChannel, executionTime);
            stopRecording();
        }

        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        actualExpectedResult.put(methodName, true, "");
        return actualExpectedResult;
    }

    /**
     * 检查声音是否达到起始值
     *
     * @param deviceChannel
     * @param startVolume
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_START"})
    default ActualExpectedResult isSoundAboveStartVolume(Integer deviceChannel, float startVolume) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        // 获取录音音量数据
        List<Float> recordedVolumes = getRecordedVolumes(deviceChannel);
        if (recordedVolumes == null || recordedVolumes.isEmpty()) {
            log.warn("声卡通道{}，尚未记录任何音量", deviceChannel);
        } else {
            // 获取最大音量值
            float maxVolume = Collections.max(recordedVolumes);
            log.info("声卡通道{}获取音量数目{}个，最大音量: {}dB", deviceChannel, recordedVolumes.size(), maxVolume);

            // 判断最大音量是否达到或超过起始值
            if (maxVolume >= startVolume) {
                log.info("声卡通道{}音量达标，已达到或超过起始值: {}dB", deviceChannel, startVolume);
                pass = true;
            } else {
                log.info("声卡通道{}音量未达标 未超过起始值: {}dB", deviceChannel, startVolume);
                // 保存失败的声音文件
                saveRecordingToFile();
            }
        }
        actualExpectedResult.put("isSoundAboveStartVolume", pass, "");
        return actualExpectedResult;
    }

    /**
     * 声音在持续时间有无达到范围值（起始到终止）
     *
     * @param deviceChannel
     * @param startVolume
     * @param endVolume
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_START_TO_END"})
    default ActualExpectedResult isSoundInVolumeRange(Integer deviceChannel, float startVolume, float endVolume) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        // 获取录音音量数据
        List<Float> recordedVolumes = getRecordedVolumes(deviceChannel);
        if (recordedVolumes == null || recordedVolumes.isEmpty()) {
            log.warn("声卡通道{}，尚未记录任何音量", deviceChannel);
        } else {
            // 获取最大音量值
            float maxVolume = Collections.max(recordedVolumes);
            log.info("声卡通道{}获取音量数目{}个，最大音量: {}dB", deviceChannel, recordedVolumes.size(), maxVolume);

            // 判断最大音量是否达到或超过起始值
            if (maxVolume >= startVolume && maxVolume <= endVolume) {
                log.info("声卡通道{}音量达标，达到目标范围音量:({},{})", deviceChannel, startVolume, endVolume);
                pass = true;
            } else {
                log.info("声卡通道{}音量未达标，未达到目标范围音量:({},{})", deviceChannel, startVolume, endVolume);
                // 保存失败的声音文件
                saveRecordingToFile();
            }
        }
        actualExpectedResult.put("isSoundInVolumeRange", pass, "");
        return actualExpectedResult;
    }

    /**
     * 声音在持续时间有无达到范围值（起始到终止）
     *
     * @param deviceChannel
     * @param startVolume
     * @param endVolume
     * @param interval
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SoundRegexRule).SOUND_START_TO_END_TIME"})
    default ActualExpectedResult isSoundInVolumeRange(Integer deviceChannel, float startVolume, float endVolume, String interval) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);  //不带单位，代表ms
        log.info("声卡通道{}查看当前音量是否达到目标范围音量:({},{}), 间隔:{}s", deviceChannel, startVolume, endVolume, seconds);
        float stepInterval = 0.1f; // 每次采样的时间间隔，单位：秒
        float elapsedTime = 0f; // 已经过去的时间
        // 初始化最大和最小音量值
        float maxVolume = Float.MIN_VALUE;
        float minVolume = Float.MAX_VALUE;
        startRecording(deviceChannel);//录音
        while (elapsedTime < seconds) {
            float currentVolume = getVolume(deviceChannel);
            if (currentVolume > maxVolume) {
                maxVolume = currentVolume;
            }
            if (currentVolume < minVolume) {
                minVolume = currentVolume;
            }
            try {
                Thread.sleep((long) (stepInterval * 1000));
            } catch (InterruptedException e) {
                log.warn("采样过程中线程被中断", e);
                Thread.currentThread().interrupt();
                break;
            }

            // 更新时间
            elapsedTime += stepInterval;
        }
        stopRecording(); // 停止录音
        // 判断最大音量是否在目标范围内
        if (maxVolume >= startVolume && maxVolume <= endVolume) {
            log.info("声卡通道{}的最大音量达到目标范围:({},{}), 最大音量: {}", deviceChannel, startVolume, endVolume, maxVolume);
            pass = true;
        } else {
            log.info("声卡通道{}在规定时间{}秒内未达到目标范围音量:({}, {}), 最大音量:{}, 最小音量:{}", deviceChannel, seconds, startVolume, endVolume, maxVolume, minVolume);
            saveRecordingToFile();// 保存失败的声音文件
        }

        actualExpectedResult.put("isSoundInVolumeRange", pass, "");
        return actualExpectedResult;
    }

    List<Float> getRecordedVolumes(Integer deviceChannel);

    String saveRecordingToFile();

    float getVolume(Integer deviceChannel) throws DeviceReadException;

    void startRecording(Integer deviceChannel);

    void stopRecording();

    /**
     * 开始声音监控
     */
    void beginSoundMonitor(Integer deviceChannel);

    /**
     * 结束声音监控
     */
    boolean endSoundMonitor(Integer deviceChannel, SoundMonitor soundMonitor) throws OperationFailNotification;
}
