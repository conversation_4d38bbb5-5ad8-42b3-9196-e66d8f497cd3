package com.desaysv.workserver.operation.handlers;

import com.desaysv.workserver.BrowserAction;
import com.desaysv.workserver.WebBrowserManager;
import com.desaysv.workserver.WinUiAutomation;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.base.variable.JavaLanguageEngine;
import com.desaysv.workserver.base.variable.VariableTrigger;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.command.CmdCommand;
import com.desaysv.workserver.utils.command.CommandResponse;
import com.desaysv.workserver.utils.command.CommandUtils;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.script.ScriptException;
import java.io.IOException;

/**
 * 通用操作执行
 */
@Component
@Slf4j
@Lazy
public class CommonOperationHandler extends OperationTarget implements ICommonHandler {

    private long startTime;

    /**
     * 等待时间
     *
     * @param seconds 时间（秒）
     * @return
     */
    public boolean waitTime(float seconds) {
        String message = String.format("等待:%fs", seconds);
        SseUtils.outputRunLog(message);
        try {
            ActionSequencesLoggerUtil.info(message);
            Thread.sleep((long) (seconds * 1000L));
        } catch (InterruptedException e) {
            log.warn("中断时间等待");
        }
        return true;
    }

    /**
     * 打印Log
     *
     * @param content Log内容
     * @return
     */
    public boolean printLog(String content) {
        log.info(content);
        return true;
    }

    /**
     * 执行命令
     *
     * @param anything 命令/文件
     * @return
     * @throws OperationFailNotification
     */
    public boolean runAnything(String anything) throws OperationFailNotification {
        return runAnything(new CmdCommand(anything));
    }

    /**
     * 显示消息对话框
     *
     * @param message 消息
     */
    public OperationResult showMessageDialog(String message) {
        OperationResult operationResult = new OperationResult();
        operationResult.setOk(true);
        operationResult.setMessage(message);
        operationResult.setPauseRequested(true);
        return operationResult;
    }


    /**
     * 发送cmd命令
     *
     * @param anything cmd命令
     * @return
     */
    public boolean runAnything(CmdCommand anything) throws OperationFailNotification {
        anything.setCommand(SystemVariableParser.parse(anything.getCommand()));
        anything.setCommand(JavaLanguageEngine.getInstance().replaceVariables(anything.getCommand()));
        if (anything.getCommand().endsWith(".py")) {
            anything.setCommand(String.format("python \"%s\"", anything.getCommand()));
        }
        try {
            CommandResponse commandResponse = CommandUtils.executeCommand(anything);
            if (!commandResponse.isOk()) {
                throw new OperationFailNotification(commandResponse.getResponse());
            }
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }
        return true;
    }

    /**
     * 执行表达式（JS）
     *
     * @param expression 表达式（JS）
     * @return
     * @throws OperationFailNotification
     */
    public boolean executeExpression(String expression) throws OperationFailNotification {
        try {
            expression = SystemVariableParser.parse(expression);
            log.info("执行表达式:{}", expression);
            Object result = JavaLanguageEngine.getInstance().eval(expression);
            String message = String.format("表达式执行结果:%s", result);
            SseUtils.outputRunLog(message);
            log.info(message);
        } catch (ScriptException e) {
            throw new OperationFailNotification(e.getMessage());
        }
        return true;
    }

    /**
     * 执行变量触发
     *
     * @param variableTrigger 变量触发器
     * @return
     */
    public boolean triggerVariableChanged(VariableTrigger variableTrigger) throws OperationFailNotification {
        log.info("判断变量触发条件:{}", variableTrigger);
        try {
            boolean ifResult = Boolean.parseBoolean(String.valueOf(JavaLanguageEngine.getInstance().eval(variableTrigger.getIfInput())));
            if (ifResult) {
                log.info("变量表达式符合条件:{}，执行:{}", variableTrigger.getIfInput(), variableTrigger.getThenInput());
                JavaLanguageEngine.getInstance().eval(variableTrigger.getThenInput());
            }
        } catch (ScriptException e) {
            throw new OperationFailNotification(e.getMessage());
        }
        return true;
    }

    /**
     * 浏览器点击
     *
     * @param browserAction
     * @throws OperationFailNotification
     */
    public void browserClick(BrowserAction browserAction) throws OperationFailNotification {
        WebBrowserManager.getInstance().click(browserAction);
    }

    /**
     * 浏览器输入文字
     *
     * @param browserAction
     * @throws OperationFailNotification
     */
    public void browserInputText(BrowserAction browserAction) throws OperationFailNotification {
        WebBrowserManager.getInstance().inputText(browserAction);
    }

    /**
     * 浏览器打开URL
     *
     * @param browserAction
     * @throws OperationFailNotification
     */
    public void browserOpenUrl(BrowserAction browserAction) throws OperationFailNotification {
        WebBrowserManager.getInstance().openUrl(browserAction);
    }

    /**
     * 浏览器检查文字
     *
     * @param browserAction
     * @throws OperationFailNotification
     */
    public void browserCheckText(BrowserAction browserAction) throws OperationFailNotification {
        WebBrowserManager.getInstance().checkText(browserAction);
    }

    /**
     * 浏览器切换标签卡
     *
     * @param browserAction
     * @throws OperationFailNotification
     */
    public void browserSwitchToTab(BrowserAction browserAction) throws OperationFailNotification {
        WebBrowserManager.getInstance().switchToTab(browserAction);
    }

    /**
     * 浏览器关闭标签卡
     *
     * @param browserAction
     * @throws OperationFailNotification
     */
    public void browserCloseTab(BrowserAction browserAction) throws OperationFailNotification {
        WebBrowserManager.getInstance().closeTab(browserAction);
    }

    /**
     * 窗口置于前台
     *
     * @param hwndName 句柄名
     */
    public void pcBringToFront(String hwndName) {
        WinUiAutomation.getInstance().bringToFront(hwndName);
    }

    /**
     * 点击pc坐标
     *
     * @param pointInt 坐标
     */
    public void pcClick(PointInt pointInt) {
        WinUiAutomation.getInstance().click(pointInt.getX(), pointInt.getY());
    }


    /**
     * 开始时间测量
     */
    public void beginTimeMeasure() {
        log.info("开始时间测量");
        startTime = System.currentTimeMillis();
    }

    /**
     * 结束时间测量
     */
    public OperationResult endTimeMeasure(String condition) throws OperationFailNotification {
        boolean ok;
        OperationResult OperationResult = new OperationResult();
        long endTime = System.currentTimeMillis();
        double timeDifferenceInSeconds = (endTime - startTime) / 1000.0;

        // 解析条件字符串
        String operator = condition.substring(0, 1); // 获取操作符
        double threshold = Double.parseDouble(condition.substring(1)); // 获取阈值

        // 根据操作符判断结果
        switch (operator) {
            case "<":
                ok = timeDifferenceInSeconds < threshold;
                break;
            case ">":
                ok = timeDifferenceInSeconds > threshold;
                break;
            case "=":
                ok = timeDifferenceInSeconds == threshold;
                break;
            default:
                throw new IllegalArgumentException("不支持的操作符号: " + operator);
        }
        log.info("结束时间测量");
        if (!ok) {
            String message = String.format("时间测试失败，要求%s,实际结果为%ss", condition, timeDifferenceInSeconds);
            OperationResult.setOk(false);
            OperationResult.setMessage(message);
            log.info(message);
            throw new OperationFailNotification(message);
        }
        log.info("时间测量成功");
        OperationResult.setOk(true);
        OperationResult.setResult(timeDifferenceInSeconds);
        return OperationResult;
    }

}
