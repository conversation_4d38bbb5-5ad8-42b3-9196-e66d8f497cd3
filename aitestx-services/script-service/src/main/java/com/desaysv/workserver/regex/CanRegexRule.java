package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.*;


/**
 * CAN动作序列语法
 */
public class CanRegexRule extends BusRegexRule {
    private static final String START = wholeMatchCaseInsensitive("Start");
    private static final String STOP = wholeMatchCaseInsensitive("Stop");
    private static final String CS_ROLLING = wholeMatchCaseInsensitive("csRolling");
    private static final String CHECKSUM = wholeMatchCaseInsensitive("checksum");
    private static final String ROLLING_COUNTER = wholeMatchCaseInsensitive("rollingCounter");
    private static final String DLC = wholeMatchCaseInsensitive("DLC");
    private static final String CYCLE_TIME = wholeMatchCaseInsensitive("CycTime");
    private static final String UPGRADE = wholeMatchCaseInsensitive("Upgrade");
    private static final String IG_SEND = wholeMatchCaseInsensitive("IGSend");
    private static final String IG_SEND_ALL = wholeMatchCaseInsensitive("IGSendAll");
    private static final String XCP = wholeMatchCaseInsensitive("XCP");
    private static final String SWITCH = wholeMatchCaseInsensitive("Switch");
    private static final String FIND_NO_KEY = wholeMatchCaseInsensitive("FindNoKey");
    private static final String FIND_THE_KEY = wholeMatchCaseInsensitive("FindTheKey");
    private static final String SET_VAR = wholeMatchCaseInsensitive("SetVar");
    private static final String CHECK_VAR = wholeMatchCaseInsensitive("CheckVar");
    private static final String SET_KEY_POSITION_NAME = wholeMatchCaseInsensitive("SetKeyPosition");
    private static final String SET_KEY_BUTTON_NAME = wholeMatchCaseInsensitive("SetKeyButton");
    private static final String SET_RDEFOGSTS_NAME = wholeMatchCaseInsensitive("SetRDefogSts");
    private static final String SET_MIRRORFOLDSTS_NAME = wholeMatchCaseInsensitive("SetMirrorFoldSts");
    private static final String SET_LAMP_SWITCH_NAME = wholeMatchCaseInsensitive("SetLampSwitch");
    private static final String GET_XCP_METHOD_NAME = wholeMatchCaseInsensitive("GetXCP");
    private static final String SET_FBL_METHOD_NAME = wholeMatchCaseInsensitive("FBL");
    private static final String SET_NODEM_METHOD_NAME = wholeMatchCaseInsensitive("NodeM");
    private static final String SET_UDS_METHOD_NAME = wholeMatchCaseInsensitive("UDS");
    private static final String SET_UDS_UDP_METHOD_NAME = wholeMatchCaseInsensitive("UDSUDP");
    private static final String SET_NM_METHOD_NAME = wholeMatchCaseInsensitive("NM");
    private static final String SET_DATA_LINK_METHOD_NAME = wholeMatchCaseInsensitive("DataLink");
    private static final String SET_TP_METHOD_NAME = wholeMatchCaseInsensitive("TP");
    private static final String GET_FBL_METHOD_NAME = wholeMatchCaseInsensitive("GetFBL");
    private static final String GET_NODEM_METHOD_NAME = wholeMatchCaseInsensitive("GetNodeM");
    private static final String GET_UDS_FUN_METHOD_NAME = wholeMatchCaseInsensitive("GetUDS");
    private static final String GET_UDS_UDP_METHOD_NAME = wholeMatchCaseInsensitive("GetUDSUDP");
    private static final String GET_NM_METHOD_NAME = wholeMatchCaseInsensitive("GetNM");
    private static final String GET_DATA_LINK_METHOD_NAME = wholeMatchCaseInsensitive("GetDataLink");
    private static final String GET_TP_METHOD_NAME = wholeMatchCaseInsensitive("GetTP");
    private static final String SET_START_UDS_TEST_METHOD_NAME = wholeMatchCaseInsensitive("StartUDS");
    private static final String RESET_CHARGE_METHOD_NAME = wholeMatchCaseInsensitive("ResetCharge");
    private static final String SET_CAN_TEMPERATURE_METHOD_NAME = wholeMatchCaseInsensitive("Temperature");
    private static final String SET_CAN_POWER_STATUS_NAME = wholeMatchCaseInsensitive("PowerState");
    private static final String SET_BOARD_CARD_INIT_NAME = wholeMatchCaseInsensitive("INIT");
    private static final String NOT_RESP = wholeMatchCaseInsensitive("NotResp");
    private static final String NOT_DTC = wholeMatchCaseInsensitive("NotDTC");
    private static final String DTC = wholeMatchCaseInsensitive("DTC");
    private static final String VERIFY_MSG = wholeMatchCaseInsensitive("VerifyMsg");
    private static final String CHECK_TURN_LAMP_NAME = wholeMatchCaseInsensitive("CheckTurnLamp");
    private static final String CHECK_FOUR_DOOR_NAME = wholeMatchCaseInsensitive("CheckFourDoor");
    private static final String SEND_EVENT_MSG_NAME = wholeMatchCaseInsensitive("SendEventMsg");

    public static final class Message {
        /**
         * 功能：发送CAN报文（周期帧）
         * 格式：CAN-Msg-X0(报文Id)-X1(报文数据)-X2(报文周期)
         */
        public static final String SEND_CAN_MESSAGE_PERIODIC = wholeCombine(MSG, group(HEX_NUMBER), group(BYTE_COMMAND), group(NUMBER));

        /**
         * 功能：发送CAN报文（事件帧）
         * 格式：CAN-Msg-X0(报文Id)-X1(报文数据)-X2(报文周期)-X3(报文帧数)
         */
        public static final String SEND_CAN_MESSAGE = wholeCombine(MSG, group(HEX_NUMBER), group(BYTE_COMMAND), group(NUMBER), group(NUMBER));

        /**
         * 功能：停止发送CAN报文
         * 格式：CAN-Stop-X0(报文Id/报文名称)
         */
        public static final String STOP_SEND_CAN_MESSAGE = wholeCombine(STOP, group(MESSAGE_OR_SIGNAL_NAME_ID));

        /**
         * 功能：Can所有报文的丢失和恢复
         * 格式：Can-Channel-X0(报文状态, 0/1, 0：Inactive, 1：Active)
         */
        public static final String CHANGE_ALL_CHANNEL_CAN_MSG = wholeCombine(CHANNEL, group(NUMBER));

        /**
         * 功能：Can所有报文的丢失和恢复（支持排除多个ECU)
         * 格式：Can-Channel-X0(报文状态, 0/1, 0：Inactive, 1：Active)-exclude-ecu-X1(ECU1)-Xn(ECUn)
         */
        public static final String CHANGE_ALL_CHANNEL_CAN_MSG_EXCLUDE = wholeCombine(CHANNEL, group(NUMBER), BaseRegexRule.EXCLUDE, add(ECU, group(more(noGroup(add(SPLITTER, ECU_NAME))))));

        /**
         * 功能：CAN单个报文的丢失和恢复（带ECU名称）
         * 格式：CAN-X0(通道)-Msg-X1(ECU名称)-X2(报文名称/id)-X3(丢失/恢复)  ----->(0:丢失 / 1：恢复)
         */
        public static final String CHANGE_CAN_SINGLE_CHANNEL_MESSAGE_STATUS = wholeCombine(MSG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER));

        /**
         * 功能：CAN单个报文的丢失和恢复（不带ECU名称）
         * 格式：CAN-X0(通道)-Msg-X1(报文名称/id)-X2(丢失/恢复)
         */
        public static final String CHANGE_CAN_SINGLE_CHANNEL_MESSAGE_STATUS_WITHOUT_ECU = wholeCombine(MSG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER));

        /**
         * 功能：CAN通道ECU所有报文的丢失和恢复
         * 格式：CAN-X0(通道)-ECU-X1(ECU名称)-X2(丢失/恢复)
         */
        public static final String CHANGE_CAN_ECU_MESSAGE_STATUS = wholeCombine(ECU, group(ECU_NAME), group(NUMBER));

        /**
         * 功能：CAN通道接收到的报文检查是否有对应通道id数据场的报文
         * 格式：CAN-X0(通道)-VerifyMsg-X1(报文Id)-X2(报文数据)-X3()
         */
        public static final String CHANGE_CAN_VERIFY_MESSAGE = wholeCombine(VERIFY_MSG, group(HEX_NUMBER), group(BYTE_X_COMMAND));
        /**
         * 功能：CAN通道接收到的报文检查是否有对应通道id数据场的报文
         * 格式：CAN-X0(通道)-VerifyMsg-X1(报文Id)-X2(报文数据)-X3(检查数量)
         */
        public static final String CHANGE_CAN_VERIFY_MESSAGE_COUNT = wholeCombine(VERIFY_MSG, group(HEX_NUMBER), group(BYTE_X_COMMAND),group(NUMBER));
    }

    /**
     * SIG动作序列
     */
    public static final class Signal {
        //***************************************************Sig***************************************************

        //循环步进&停止循环步进
        //TODO：不带间隔时间（使用周期时间）的循环步进
        /**
         * 功能：循环步进CAN信号值（带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-Step-Cycle-X4(信号起始值)-X5(信号终止值)-X6(步进值)-X7(步进间隔时间)
         */
        public static final String STEP_CAN_SIGNAL_CYCLE = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
                RegexRuleConstants.STEP, RegexRuleConstants.CYCLE, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER), group(ANY_TIME));

        /**
         * 功能：循环步进CAN信号值（不带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(报文名)-X2(信号名)-Step-Cycle-X3(信号起始值)-X4(信号终止值)-X5(步进值)-X7(步进间隔时间)
         */
        public static final String STEP_CAN_SIGNAL_CYCLE_WITHOUT_ECU = wholeCombine(SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
                RegexRuleConstants.STEP, RegexRuleConstants.CYCLE, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER), group(ANY_TIME));
        /**
         * 功能：停止循坏步进CAN信号值（带ECU名称）
         * 格式：CAN-Step-Cycle-Stop-X1(ECU名称)-X2(报文名)-X3(信号名)
         */
        public static final String STOP_STEP_CAN_SIGNAL_CYCLE = wholeCombine(RegexRuleConstants.STEP, RegexRuleConstants.CYCLE, STOP, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID));

        /**
         * 功能：停止循坏步进CAN信号值（不带ECU名称）
         * 格式：CAN-Step-Cycle-Stop-X1(报文名)-X2(信号名)
         */
        public static final String STOP_STEP_CAN_SIGNAL_CYCLE_WITHOUT_ECU = wholeCombine(RegexRuleConstants.STEP, RegexRuleConstants.CYCLE, STOP, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID));


        //改变信号值——固定
        /**
         * 功能：改变CAN信号值，固定（带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-X4(信号值)
         */
        public static final String CHANGE_CAN_SIGNAL = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER_WITH_HEX));

        /**
         * 功能：改变CAN信号值，固定（不带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(报文名)-X2(信号名)-X3(信号值)
         */
        public static final String CHANGE_CAN_SIGNAL_WITHOUT_ECU = wholeCombine(SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER_WITH_HEX));

        //改变信号值——随机
        /**
         * 功能：改变CAN信号值，随机（带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-Random-X3(随机下限)-X4(随机上限)
         */
        public static final String RANDOM_CAN_SIGNAL = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), RegexRuleConstants.RANDOM, group(ALL_NUMBER), group(ALL_NUMBER));

        /**
         * 功能：改变CAN信号值，随机（不带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-Random-X3(随机下限)-X4(随机上限)
         */
        public static final String RANDOM_CAN_SIGNAL_WITHOUT_ECU = wholeCombine(SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), RegexRuleConstants.RANDOM, group(ALL_NUMBER), group(ALL_NUMBER));

        //改变信号值——非循环步进
        /**
         * 功能：步进CAN信号值（带ECU名称）, 不带间隔时间
         * 格式：CAN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-Step-X4(信号起始值)-X5(信号终止值)-X6(步进值)
         */
        public static final String STEP_CAN_SIGNAL_WITHOUT_TIME = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
                RegexRuleConstants.STEP, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER));

        /**
         * 功能：步进CAN信号值（带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-Step-X4(信号起始值)-X5(信号终止值)-X6(步进值)-X7(步进间隔时间)
         */
        public static final String STEP_CAN_SIGNAL = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
                RegexRuleConstants.STEP, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER), group(ANY_TIME));

        /**
         * 功能：步进CAN信号值（不带ECU名称）
         * 格式：CAN-X0(通道)-Sig-X1(报文名)-X2(信号名)-Step-X3(信号起始值)-X4(信号终止值)-X5(步进值)-X7(步进间隔时间)
         */
        public static final String STEP_CAN_SIGNAL_WITHOUT_ECU = wholeCombine(SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
                RegexRuleConstants.STEP, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER), group(ANY_TIME));

        /**
         * 功能：步进CAN信号值（不带ECU名称）， 不带间隔时间
         * 格式：CAN-X0(通道)-Sig-X1(报文名)-X2(信号名)-Step-X3(信号起始值)-X4(信号终止值)-X5(步进值)
         */
        public static final String STEP_CAN_SIGNAL_WITHOUT_ECU_AND_TIME = wholeCombine(SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
                RegexRuleConstants.STEP, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER));


        //***************************************************GetSig***************************************************

        /**
         * 功能：接收CAN信号期望值：固定（带ECU名称）
         * 格式：CAN-X0(通道)-GetSig-X1(ECU名称)-X2(报文名)-X3(信号名)-X4(期望信号值)
         */
        public static final String COMPARE_CAN_SIGNAL = wholeCombine(GET_SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME), group(MESSAGE_OR_SIGNAL_NAME), group(ALL_NUMBER_WITH_HEX));

        /**
         * 功能：接收CAN信号期望值：固定（不带ECU名称）
         * 格式：CAN-X0(通道)-GetSig-X1(报文名)-X2(信号名)-X3(期望信号值)
         */
        public static final String COMPARE_CAN_SIGNAL_WITHOUT_ECU = wholeCombine(GET_SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER_WITH_HEX));

        /**
         * 功能：接收CAN信号期望值：在一定范围内（带ECU名称）
         * 格式：CAN-X0(通道)-GetSig-X1(ECU名称)-X2(报文名)-X3(信号名)-X4(期望信号下限值)-X5(期望信号上限值)
         */
        public static final String COMPARE_CAN_SIGNAL_RANGE = wholeCombine(GET_SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER), group(NUMBER));

        /**
         * 功能：接收CAN信号期望值：在一定范围内（不带ECU名称）
         * 格式：CAN-X0(通道)-GetSig-X1(报文名)-X2(信号名)-X3(期望信号下限值)-X4(期望信号上限值)
         */
        public static final String COMPARE_CAN_SIGNAL_RANGE_WITHOUT_ECU = wholeCombine(GET_SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER), group(NUMBER));
    }


    public static final class Property {
        /**
         * 功能：CAN报文checksum和RollingCounter控制（带ECU名称）
         * 格式：CAN-X0(通道)-csRolling-X1(ECU名称)-X2(报文id)-X3(Checksum状态，0/1，1表示正确，0表示错误)-X4(RollingCounter状态，0/1，0表示错误，1表示正确)
         */
        public static final String CHANGE_CAN_MESSAGE_CSROLLING = wholeCombine(CS_ROLLING, group(ECU_NAME), group(HEX_NUMBER), group(NUMBER), group(NUMBER));

        /**
         * 功能：CAN报文checksum和RollingCounter控制（不带ECU名称）
         * 格式：CAN-X0(通道)-csRolling-X1(报文名称/id)-checksum-X2(checksum状态)-rollingCounter-X3(rollingCounter状态)
         */
        public static final String CHANGE_CAN_MESSAGE_CSROLLING_WITHOUT_ECU = wholeCombine(CS_ROLLING, group(MESSAGE_OR_SIGNAL_NAME_ID), CHECKSUM, group(NUMBER), ROLLING_COUNTER, group(NUMBER));

        /**
         * 功能：更改报文DLC（带ECU名称）
         * 格式：CAN-X0(通道)-DLC-X1(ECU名称)-X2(报文名称/id)-X3(报文DLC)
         */
        public static final String CHANGE_CAN_DLC = wholeCombine(DLC, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER));

        /**
         * 功能：更改报文DLC（不带ECU名称）
         * 格式：CAN-X0(通道)-DLC-X1(报文名称/id)-X2(报文DLC)
         */
        public static final String CHANGE_CAN_DLC_WITHOUT_ECU = wholeCombine(DLC, group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER));

        /**
         * 功能：更改报文周期（带ECU名称）
         * 格式：CAN-X0(通道)-CycTime-X1(ECU名称)-X2(报文名称/id)-X3(报文周期)
         */
        public static final String CHANGE_CAN_CYCLE_TIME = wholeCombine(CYCLE_TIME, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER));

        /**
         * 功能：更改报文周期（不带ECU名称）
         * 格式：CAN-X0(通道)-CycTime-X1(报文名称/id)-X2(报文周期)
         */
        public static final String CHANGE_CAN_CYCLE_TIME_WITHOUT_ECU = wholeCombine(CYCLE_TIME, group(MESSAGE_OR_SIGNAL_NAME_ID), group(NUMBER));

        /**
         * 功能：更改报文周期，随机（带ECU名称）
         * 格式：CAN-X0(通道)-CycTime-X1(报文名称/id)-Random-X2(报文周期下限)-X3(报文周期上限)
         */
        public static final String RANDOM_CHANGE_CAN_CYCLE_TIME = wholeCombine(CYCLE_TIME, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), RegexRuleConstants.RANDOM, group(NUMBER), group(NUMBER));

        /**
         * 功能：更改报文周期，随机（不带ECU名称）
         * 格式：CAN-X0(通道)-CycTime-X1(报文名称/id)-Random-X2(报文周期下限)-X3(报文周期上限)
         */
        public static final String RANDOM_CHANGE_CAN_CYCLE_TIME_WITHOUT_ECU = wholeCombine(CYCLE_TIME, group(MESSAGE_OR_SIGNAL_NAME_ID), RegexRuleConstants.RANDOM, group(NUMBER), group(NUMBER));

    }

    public static final class PTS {
        /**
         * 功能：PTS写入（带ECU/报文id）
         * 格式：CAN-X0(通道)-PTSTX-X1(ECU名称)-X2(报文id)-X3(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_CAN_PTS = wholeCombine(PTSTX, group(ECU_NAME), group(HEX_NUMBER), group(BYTE_X_COMMAND));
        /**
         * 功能：PTS写入（只带ECU）
         * 格式：CAN-X0(通道)-PTSTX-X1(ECU名称)-X2(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_CAN_PTS_WITH_ECU = wholeCombine(PTSTX, group(ECU_NAME), group(BYTE_X_COMMAND));

        /**
         * 功能：PTS写入（不带ECU/报文id）
         * 格式：CAN-X0(通道)-PTSTX-X1(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_CAN_PTS_WITHOUT_ECU = wholeCombine(PTSTX, group(BYTE_X_COMMAND));

        /**
         * 功能：PTS写入（带报文id）
         * 格式：CAN-X0(通道)-PTSTX-X1(报文id)-X2(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_CAN_PTS_BY_MESSAGE_ID = wholeCombine(PTSTX, group(HEX_NUMBER), group(BYTE_X_COMMAND));

        /**
         * 功能：PTS期望反馈指令
         * 格式：CAN-X0(通道)-PTSRX-X1(PTS字节的指令)
         */
        public static final String COMPARE_CAN_PTS = wholeCombine(PTSRX, group(BYTE_X_COMMAND));

        /**
         * 功能：PTS期望无反馈指令
         * 格式：CAN-X0(通道)-PTSRX-NA
         */
        public static final String COMPARE_CAN_PTS_NA = wholeCombine(PTSRX, RegexRuleConstants.NA);

        /**
         * 功能：期望总线上出现某个报文id
         * 格式：CAN-X0(通道)-CheckMsg-Exist-X1(报文id)
         */
        public static final String EXIST_CAN_MESSAGE_ID = wholeCombine(CHECK_MSG, EXIST, group(HEX_NUMBER));

        /**
         * 功能：期望总线上不出现某个报文id
         * 格式：CAN-X0(通道)-CheckMsg-NotExist-X1(报文id)
         */
        public static final String NOT_EXIST_CAN_MESSAGE_ID = wholeCombine(CHECK_MSG, NOT_EXIST, group(HEX_NUMBER));

        /**
         * 功能：持续100ms检测总线上出现某个报文id
         * 格式：CAN-X0(通道)-CheckMsg-Exist-X1(报文id)-last-100ms
         */
        public static final String LAST_CHECK_EXIST_CAN_MESSAGE_ID = wholeCombine(CHECK_MSG, EXIST, group(HEX_NUMBER), LAST, MILLI_TIME);

        /**
         * 功能：持续100ms检测总线上出现某个报文id
         * 格式：CAN-X0(通道)-CheckMsg-Exist-X1(报文id)-last-100ms
         */
        public static final String LAST_CHECK_NOT_EXIST_CAN_MESSAGE_ID = wholeCombine(CHECK_MSG, NOT_EXIST, group(HEX_NUMBER), LAST, MILLI_TIME);

        /**
         * 功能：PTS期望无反馈指令（带报文id）
         * 格式：CAN-X0(通道)-PTSRX-X1(报文id)-X2(PTS字节的指令)
         */
        public static final String COMPARE_CAN_PTS_WITH_MESSAGE_ID = wholeCombine(PTSRX, group(HEX_NUMBER), group(BYTE_X_COMMAND));

        /**
         * 功能：PTS期望无报文值
         * 格式：CAN-X0(通道)-PTSRX-X1(报文id)-NOT_EXIST-X2(PTS字节的指令)
         */
        public static final String COMPARE_CAN_PTS_WITHOUT_MESSAGE_VALUE = wholeCombine(PTSRX, group(HEX_NUMBER), NOT_EXIST, group(BYTE_X_COMMAND));

        /**
         * 功能：PTS期望反馈指令
         * 格式：CAN-X0(通道)-PTSRX-X1(PTS字节的指令)-byteX(需要检查的范围的字节)-X2(标准值)-X3(幅度)
         */
        public static final String COMPARE_CAN_PTS_HEX_BYTE = wholeCombine(PTSRX, group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(HEX_NUMBER), group(HEX_NUMBER));

        /**
         * 功能：PTS期望反馈指令
         * 格式：CAN-X0(通道)-PTSRX-X1(PTS字节的指令)-byteX(需要检查的范围的字节)-X2(字节下限)-X3(字节上限)
         */
        public static final String COMPARE_CAN_PTS_BYTE = wholeCombine(PTSRX, group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(NUMBER), group(NUMBER));

        /**
         * 功能：PTS期望反馈指令
         * 格式：CAN-X0(通道)-PTSRX-X1(报文id)-X2(PTS字节的指令)-byteX(需要检查的范围的字节)-X3(字节下限)-X4(字节上限)
         */
        public static final String COMPARE_CAN_PTS_BYTE_WITH_MESSAGE_ID = wholeCombine(PTSRX, group(HEX_NUMBER), group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(NUMBER), group(NUMBER));

        /**
         * 功能：PTS期望反馈指令
         * 格式：CAN-X0(通道)-PTSRX-X1(报文id)-X2(PTS字节的指令)-byteX(需要检查的范围的字节)-X3(标准值)-X4(幅度)
         */
        public static final String COMPARE_CAN_PTS_HEX_BYTE_WITH_MESSAGE_ID = wholeCombine(PTSRX, group(HEX_NUMBER), group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(HEX_NUMBER), group(HEX_NUMBER));

//        /**
//         * 功能：PTS TXRX写入直接检查反馈（不带报文id）
//         * 格式：CAN-X0(通道)-PTSTX-X1(PTS字节的指令，可加空格))-PTSRX-X2(PTS字节的指令，可加空格)
//         */
//        public static final String COMPARE_CAN_PTS_TX_RX = wholeCombine(PTSTX, group(BYTE_X_COMMAND), PTSRX, group(BYTE_X_COMMAND));
//        /**
//         * 功能：PTS TXRX写入直接检查反馈（带报文id）
//         * 格式：CAN-X0(通道)-PTSTX-X1(报文id)-X2(PTS字节的指令，可加空格)-PTSRX-X3(报文id)-X4(PTS字节的指令，可加空格)
//         */
//        public static final String COMPARE_CAN_PTS_TX_RX_WITH_MESSAGE_ID = wholeCombine(PTSTX, group(HEX_NUMBER), group(BYTE_X_COMMAND), PTSRX, group(HEX_NUMBER), group(BYTE_X_COMMAND));
    }

    /**
     * CAN总线某通道单个IG模块报文的发送和停止
     * 格式：CAN-X0(通道)-IGSend-X1-X2(X1:IG选项卡名称， X2:表示动作；0:发送,1:关闭)
     */
    public static final String SET_IG_SEND = wholeCombine(IG_SEND, group(WORD), group(ALL_NUMBER));

    /**
     * CAN总线某通道所有IG模块报文的发送和停止
     * 格式：CAN-X0(通道)-IGSendAll-X1(X1:表示动作；0:发送,1:关闭)
     */
    public static final String SET_IG_SEND_ALL = wholeCombine(IG_SEND_ALL, group(ALL_NUMBER));

    /**
     * 功能：激活XCP功能
     * 格式：CAN-XCP-Switch-x1(0:XCP功能关闭,1:XCP功能打开)
     */
    public static final String SET_XCP_FUN_SWITCH = wholeCombine(XCP, SWITCH, group(ALL_NUMBER));

    /**
     * 功能：对XCP变量赋值
     * 格式：CAN-XCP-SetVar-VarName(XCP变量名)-Value(需要设置的值)
     */
    public static final String SET_XCP_VAR = wholeCombine(XCP, SET_VAR, group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER));

    /**
     * 功能：对XCP变量赋值
     * 格式：CAN-XCP-Switch-x(XCP功能状态)-SetVar-XCPVarName(XCP变量名)-Value(需要设置的值)
     */
    public static final String SET_XCP_FUN_SWITCH_AND_VAR = wholeCombine(XCP, SWITCH, group(ALL_NUMBER), SET_VAR, group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER));

    /**
     * 功能：读取XCP变量
     * 格式：CAN-XCP-CheckVar-XCPVarName(XCP变量名)-Value(需要检查的值)
     */
    public static final String CHECK_XCP_VAR = wholeCombine(XCP, CHECK_VAR, group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER));

    /**
     * 功能：读取XCP变量
     * 格式：CAN-XCP-Switch-x(XCP功能状态)-CheckVar-XCPVarName(XCP变量名)-Value(葡要检查的值)-X2(等待时间.ms)-Switch-x(XCP功能状态)
     */
    public static final String CHECK_XCP_VAR_WITH_TIME = wholeCombine(XCP, SWITCH, group(ALL_NUMBER), CHECK_VAR, group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER), group(ANY_TIME), SWITCH, group(ALL_NUMBER));

    /**
     * 功能：检测天线找不到钥匙的工作周期;
     * 格式：CAN-XCP-FindNoKey-X (X为检测时间)
     * 注意：该序列的超时时间需要设置大于31s变量
     */
    public static final String CHECK_XCP_FIND_NO_KEY = wholeCombine(XCP, FIND_NO_KEY, group(ALL_NUMBER));

    /**
     * 功能：检测天线到钥匙后的工作周期;
     * 格式：CAN-XCP-FindTheKey-X (X为检测时间)
     * 注意：该序列的超时时间需要设置大于31s变量
     */
    public static final String CHECK_XCP_FIND_THE_KEY = wholeCombine(XCP, FIND_THE_KEY, group(ALL_NUMBER));
    
    /**
     * 功能：控制含有钥匙位置信息的报文发送
     * 格式：CAN-SetKeyPosition-x1 (0:无钥匙,1:钥匙在车内,2:钥匙在主驾车外,3:钥匙在后尾车外,4:不合法钥匙在车内,5:不合法钥匙在主驾外,6:不合法钥匙在车尾外)
     */
    public static final String SET_KEY_POSITION = wholeCombine(SET_KEY_POSITION_NAME, group(ALL_NUMBER));

    /**
     * 功能：控制含有按键位置信息的报文发送
     * 格式：CAN-SetKeyButton-x1 (0:锁车Lock,1:解锁Unlock,2:寻车Findcar,3:后尾门Tallgate)
     */
    public static final String SET_KEY_BUTTON = wholeCombine(SET_KEY_BUTTON_NAME, group(ALL_NUMBER));
    /**
     * 作用:设置后除霜状态
     * 格式:CAN-SetRDefogSts-X0(0:后除霜关闭,1:后除霜打开)
     * 例如：CAN-SetRDefogSts-0
     */
    public static final String SET_RDEFOGSTS = wholeCombine(SET_RDEFOGSTS_NAME, group(ALL_NUMBER));

    /**
     * 作用:设置后视镜状态
     * 格式：CAN-SetMirrorFoldSts-X(0;UnFlod，1;flod)
     * 例如：CAN-SetMirrorFoldSts-UnFlod
     */
    public static final String SET_MIRRORFOLDSTS = wholeCombine(SET_MIRRORFOLDSTS_NAME, group(MIRROR_FOLD_STATUS));

    /**
     * 作用:Lamp序列
     * 格式：CAN-SetLampSwitch-X1, X1为OFF、IHBC、AUTO、POSITION、LOW、HIGHT、HGHTOFF、FLASH、FLASHOFF、FRFOG、FRRRFOG、FOGOFF、LEFTTURN、RIGHTTURN、TURNOFF
     * 例如：CAN-SetLampSwitch-OFF
     */
    public static final String SET_LAMP_SWITCH = wholeCombine(SET_LAMP_SWITCH_NAME, group(LAMP_SWITCH_STATUS));

    /**
     * 作用:检测转向灯工作周期
     * 格式：CAN-CheckTurnLamp-X1-X2-X3（X1为转向灯类型：左转向灯LeftTurn/右转向灯RightTurn/双闪DoubleFlash；X2为点亮时间：ms；X3为检测周期）
     * 例如：CAN-CheckTurnLamp-LeftTurn-400-3  //表示检测左转向灯，闪烁400ms，工作3个周期
     */
    public static final String CHECK_TURN_LAMP = wholeCombine(CHECK_TURN_LAMP_NAME, group(TURN_LAMP_STATUS), group(ALL_NUMBER), group(ALL_NUMBER));

    /**
     * 作用：检测四门解/闭锁。
     * 格式：CAN-CheckFourDoor-X1 （X1为解锁UnLock/闭锁Lock）
     * 例如：CAN-CheckFourDoor-UnLock   //表示检测四门解锁驱动
     */
    public static final String CHECK_FOUR_DOOR = wholeCombine(CHECK_FOUR_DOOR_NAME, group(FOUR_DOOR_STATUS));

    /**
     * 作用：发送事件报文
     * 格式：CAN-X0-SendEventMsg-X1-X2-X3-X4（X0为通道，X1为报文ID，X2为发送周期 ，X3为发送帧数 ，X4为报文数据）
     * 例如：CAN-1-SendEventMsg-0x100-20-3-0000000000000000  //模拟发送3帧CAN通道1报文0x100: 00 00 00 00 00 00 00 00 00，间隔20ms
     */
    public static final String SEND_EVENT_MSG = wholeCombine(SEND_EVENT_MSG_NAME, group(HEX_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER), group(BYTE_X_COMMAND));


    /**
     * 功能：XCP变量写入
     * 格式：CAN-XCP-X0(XCP的变量组名称)-X1(XCP的变量名称)-X2(变量的值，负数用括号)
     */
//    public static final String CHANGE_XCP = wholeCombine(XCP, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER));

    /**
     * 功能：XCP变量读取，固定判断
     * 格式：CAN-GetXCP-X0(XCP的变量组名称)-X1(XCP的变量名称)-X2(变量的值，负数用括号)
     */
//    public static final String COMPARE_XCP = wholeCombine(GET_XCP_METHOD_NAME, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER));

    /**
     * 功能：XCP变量读取，随机判断
     * 格式：CAN-GetXCP-X0(XCP的变量组名称)-X1(XCP的变量名称)-Random-X2(变量的值下限，负数用括号)-X3(变量的值上限，负数用括号)
     */
    public static final String COMPARE_XCP_RANGE = wholeCombine(GET_XCP_METHOD_NAME, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), RegexRuleConstants.RANDOM, group(ALL_NUMBER), group(ALL_NUMBER));

    public static final String SET_CAN_FBL = wholeCombine(SET_FBL_METHOD_NAME, group(PARAM));
    public static final String SET_CAN_NODEM = wholeCombine(SET_NODEM_METHOD_NAME, group(PARAM));
    public static final String SET_CAN_UDS_FUN = wholeCombine(SET_UDS_METHOD_NAME, group(ALPHABET), group(PARAM));
    public static final String SET_CAN_UDS_27_SERVER_FUN = wholeCombine(SET_UDS_METHOD_NAME, group(ALPHABET), group(PARAM), group(NUMBER));
    public static final String SET_CAN_UDS_UDP = wholeCombine(SET_UDS_UDP_METHOD_NAME, group(PARAM));
    public static final String SET_CAN_NM = wholeCombine(SET_NM_METHOD_NAME, group(PARAM));
    public static final String SET_CAN_DATA_LINK = wholeCombine(SET_DATA_LINK_METHOD_NAME, group(PARAM));
    public static final String SET_CAN_TP = wholeCombine(SET_TP_METHOD_NAME, group(PARAM));
    public static final String SET_CAN_UDS_LOG = wholeCombine(SET_UDS_LOG_METHOD_NAME, group(NUMBER));

    //SET_UDS_LOG_NAME_METHOD_NAME 自定义
    public static final String SET_UDS_LOG_NAME = wholeCombine(SET_UDS_LOG_NAME_METHOD_NAME, group(NAME_VARIABLE));
    public static final String GET_CAN_FBL = wholeCombine(GET_FBL_METHOD_NAME, group(PARAM), group(NUMBER));
    public static final String GET_CAN_NODEM = wholeCombine(GET_NODEM_METHOD_NAME, group(PARAM), group(NUMBER));
//    public static final String GET_CAN_UDS_FUN_CHECK_NOT_RESP = wholeCombine(GET_UDS_FUN_METHOD_NAME, NOT_RESP);
    public static final String GET_CAN_UDS_FUN_CHECK_NOT_DTC = wholeCombine(GET_UDS_FUN_METHOD_NAME, NOT_DTC, group(NAME_VARIABLE));
    public static final String GET_CAN_UDS_FUN_CHECK_DTC = wholeCombine(GET_UDS_FUN_METHOD_NAME, DTC, group(NAME_VARIABLE));
    public static final String GET_CAN_UDS_FUN = wholeCombine(GET_UDS_FUN_METHOD_NAME, group(NAME_VARIABLE));
    public static final String GET_CAN_UDS_EXPLAIN_FUN = wholeCombine(GET_UDS_FUN_METHOD_NAME, EXPLAIN_KEYWORD, group(NAME_VARIABLE));
    public static final String GET_CAN_UDS_UDP = wholeCombine(GET_UDS_UDP_METHOD_NAME, group(PARAM), group(NUMBER));
    public static final String GET_CAN_NM = wholeCombine(GET_NM_METHOD_NAME, group(PARAM), group(NUMBER));
    public static final String GET_CAN_DATA_LINK = wholeCombine(GET_DATA_LINK_METHOD_NAME, group(PARAM), group(NUMBER));
    public static final String GET_CAN_TP = wholeCombine(GET_TP_METHOD_NAME, group(PARAM), group(NUMBER));

    //CAN-X0-Upgrade-fileName
    public static final String NOTIFICATION_UPGRADE = wholeCombine(UPGRADE, group(NAME_VARIABLE));
    //CAN LOG
    public static final String SET_CAN_LOG_NAME = wholeCombine(SET_CAN_LOG_NAME_METHOD_NAME, group(NAME_VARIABLE));

    public static final String SET_CAN_LOG = wholeCombine(SET_CAN_LOG_METHOD_NAME, group(NUMBER));

    public static final String SET_START_UDS_TEST = wholeCombine(SET_START_UDS_TEST_METHOD_NAME, group(NUMBER));

    public static final String SET_RESET_CHARGE = wholeCombine(RESET_CHARGE_METHOD_NAME, group(NUMBER));

    public static final String SET_TEMPERATURE = wholeCombine(SET_CAN_TEMPERATURE_METHOD_NAME, group(NUMBER), group(ALL_NUMBER));
    //宝腾项目电源状态有好几种，要在不同的电源状态下进行测试。需要传电源状态参数传到CANoe
    public static final String SET_POWER_STATE = wholeCombine(SET_CAN_POWER_STATUS_NAME, group(RegexRuleConstants.POWER_STATE));
    //初始化板卡，需要传初始化参数类型传到CANoe
    public static final String SET_BOARD_CARD_INIT = wholeCombine(SET_BOARD_CARD_INIT_NAME, group(RegexRuleConstants.INIT_STATE));
    /**
     * 功能：开关通道
     * 格式：CAN-X0(通道)-STOP
     */
    public static final String STOP_CAN_CHANNEL = wholeCombine(STOP);

    /**
     * 功能：开关通道
     * 格式：CAN-X0(通道)-START
     */
    public static final String START_CAN_CHANNEL = wholeCombine(START);

    public static void main(String[] args) {
//        String regex = "^(?:\\b(?i)Sig(?!\\+)\\b)[-——]((?!0x)[a-zA-Z0-9]+)[-——]([\\u4E00-\\u9FA5A-Za-z0-9_]+)[-——]([\\u4E00-\\u9FA5A-Za-z0-9_]+)[-——](\\(\\-\\d+(\\.\\d+)?\\)|\\d+(\\.\\d+)?)$";
//        String line = "Sig-0x122-BCM2_General-Driver_Door_Switch_Status-0";
        String line = "GetSig-EMS_DriveECOInfo-EMS_Drive_Coach_SpeedRPM-533";
        System.out.println(BaseRegexRule.match(line, Signal.COMPARE_CAN_SIGNAL_WITHOUT_ECU));
        String line2 = "getSig-Display_3_02-DU3_Betriebsdauer_min-XXX0-59";

        System.out.println(BaseRegexRule.match(line2, Signal.COMPARE_CAN_SIGNAL));
        String line3 = "59";

        System.out.println(BaseRegexRule.match(line3, MESSAGE_OR_SIGNAL_NAME_ID));
        String line4 = "0x23";

        System.out.println(BaseRegexRule.match(line4, MESSAGE_OR_SIGNAL_NAME_ID));
    }
}
