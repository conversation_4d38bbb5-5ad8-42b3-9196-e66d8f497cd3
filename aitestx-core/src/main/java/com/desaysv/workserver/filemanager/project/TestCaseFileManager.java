package com.desaysv.workserver.filemanager.project;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.base.operation.base.TestScriptFileContent;
import com.desaysv.workserver.utils.FileUtils;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;

/**
 * TestCase文件管理器（线程安全）
 */
@Slf4j
public class TestCaseFileManager extends ProjectFileManager {


    public File testCaseFileDBPath;

    public TestCaseFileManager(String projectName) {
        super(projectName);
    }


    /**
     * 初始化TestCase路径
     */
    @Override
    protected void initSubPaths(String dynamicFolderName) {
        testCaseFileDBPath = createFolder(fileDbPath, "testcases");
    }

    /**
     * 获取TestCase文件
     *
     * @param testCaseFileName TestCase文件名
     * @return TestCase文件
     */
    public File getTestCaseFile(String testCaseFileName) {
        testCaseFileName = FileUtils.replaceSpecialChar(testCaseFileName);
        return new File(testCaseFileDBPath, testCaseFileName + JSON_SUFFIX_WITH_DOT);
    }

    /**
     * 重命名TestCase文件
     *
     * @param oldTestCasFileName  旧TestCase文件名
     * @param newTestCaseFileName 新TestCase文件名
     * @return 是否重命名成功
     */
    public boolean renameTestCaseFile(String oldTestCasFileName, String newTestCaseFileName) {
        return ThreadSafeFileUtils.renameFile(getTestCaseFile(oldTestCasFileName), getTestCaseFile(newTestCaseFileName));
    }

    /**
     * 更新TestCase文件
     *
     * @param testCaseFileName      测试脚本文件名
     * @param testScriptFileContent 测试脚本
     */
    public void updateTestCaseFile(String testCaseFileName, TestScriptFileContent testScriptFileContent) {
        //TODO: JSON.writeJSONString()
        File jsonFile = getTestCaseFile(testCaseFileName);
        String content = testScriptFileContent == null ? "" : JSON.toJSONString(testScriptFileContent);

        // 使用文件锁写入内容
        ThreadSafeFileUtils.writeFileFromString(jsonFile, content, false);
    }

    /**
     * 复制TestCase文件 TODO：测试线程安全版本
     *
     * @param srcFilePah 源文件路径
     * @return 是否复制成功
     */
    public boolean copyTestCaseFile(String srcFilePah) {
        File srcFile = new File(srcFilePah);
        File dstFile = new File(testCaseFileDBPath, srcFile.getName());
        if (dstFile.exists()) {
            return false;
        }
        try {
            org.apache.commons.io.FileUtils.copyFileToDirectory(srcFile, testCaseFileDBPath);
            return true;
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除TestCase文件
     *
     * @param testCaseFileName TestCase文件名
     * @return 是否删除成功
     */
    public boolean deleteTestCaseFile(String testCaseFileName) {
        return ThreadSafeFileUtils.deleteFile(testCaseFileName);
    }

    /**
     * 清除所有TestCase文件
     */
    public void clearAllTestCaseFiles() {
        ThreadSafeFileUtils.deleteAllFile(testCaseFileDBPath);
    }

    /**
     * 读取TestCase文件
     *
     * @param testCaseFileName TestCase文件名
     * @return TestCase文件内容
     */
    public TestScriptFileContent readTestCaseFile(String testCaseFileName) {
        File jsonFile = getTestCaseFile(testCaseFileName);

        String fileContent = ThreadSafeFileUtils.readFileToString(jsonFile.getAbsolutePath());
        TestScriptFileContent testScriptFileContent;
        try {
            testScriptFileContent = JSON.parseObject(fileContent, TestScriptFileContent.class);
            if (testScriptFileContent == null) {
                testScriptFileContent = new TestScriptFileContent();
                testScriptFileContent.setTestCycle(-1);
            }
        } catch (Exception e) {
            testScriptFileContent = new TestScriptFileContent();
            testScriptFileContent.setTestCycle(-1);
            log.warn("测试脚本错误:{}", fileContent);
            log.warn(e.getMessage(), e);
        }
        testScriptFileContent.setFileContent(fileContent);
        return testScriptFileContent;
    }

    /**
     * 使用NIO文件锁进行文件操作
     *
     * @param file      要操作的文件
     * @param operation 文件操作回调
     * @param readOnly  是否为只读操作
     * @return 操作结果
     */
    private <T> T withFileLock(File file, FileOperation<T> operation, boolean readOnly) {
        String mode = readOnly ? "r" : "rw";
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, mode);
             FileChannel channel = randomAccessFile.getChannel()) {

            // 获取文件锁
            FileLock lock = readOnly ? channel.lock(0L, Long.MAX_VALUE, true) : channel.lock();
            try {
                return operation.execute(randomAccessFile, channel);
            } finally {
                if (lock != null && lock.isValid()) {
                    lock.release();
                }
            }
        } catch (IOException e) {
            log.error("文件锁操作失败: " + file.getAbsolutePath(), e);
            return null;
        }
    }

    /**
     * 文件操作接口
     */
    private interface FileOperation<T> {
        T execute(RandomAccessFile file, FileChannel channel) throws IOException;
    }

}
