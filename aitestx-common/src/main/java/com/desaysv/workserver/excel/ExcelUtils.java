package com.desaysv.workserver.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.desaysv.workserver.entity.ColumnNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel工具类
 */
@Slf4j

public class ExcelUtils {
    private static final ColumnNameConstants columnNameConstants = ColumnNameConstants.getInstance();
    private static final String ROW_NUMBER = "row_number";
    private static final String UUID_KEY = "uuid";
//    private static final String TABLE_NAME = "tableName";

    /**
     * 读取Excel
     *
     * @param excelEntity excel选项
     * @return Excel数据
     */
    public static Map<String, ExcelSheetTable> readExcel(ExcelEntity excelEntity) {
        CommonExcelReader commonExcelReader = CommonExcelReader.getExcelReader(excelEntity);
        if (commonExcelReader != null) {
            List<String> sheetNames = excelEntity.getSheetNames();
            for (String sheetName : sheetNames) {
                log.info("读取表格：{}", sheetName);
                commonExcelReader.read(excelEntity.getHeaderRowNumber(), sheetName);
            }
            //            List<Integer> targetColumns = Arrays.asList(5, 6, 7); //TODO：由客户端指定
//            List<List<String>> targetList = new ArrayList<>();
//            for (HashMap<Integer, String> map : commonExcelReader.getExcelList()) {
//                List<String> rowData = new ArrayList<>();
//                for (Map.Entry<Integer, String> entry : map.entrySet()) {
//                    if (targetColumns.contains(entry.getKey())) {
//                        rowData.add(entry.getValue());
//                    }
//                }
//                targetList.add(rowData);
//            }
//            targetList.removeIf(list -> list.stream().allMatch(Objects::isNull));
            return commonExcelReader.getExcelSheetMap();
        }
        return new HashMap<>();
    }

    public static void adjustZipRatio() {
        // 设置最小压缩比为 0.001
        ZipSecureFile.setMinInflateRatio(0.001);
        ZipSecureFile.setMaxFileCount(10000);//允许最多条目数
    }

    /**
     * 获取工作簿名称列表
     *
     * @param excelPath Excel路径
     * @return 工作簿名称列表
     */
    public static List<String> getSheetNames(String excelPath) {
        File excelFile = new File(excelPath);
        if (!excelFile.exists()) {
            return new ArrayList<>();
        }
        adjustZipRatio();
        ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(excelFile);
        try (ExcelReader excelReader = excelReaderBuilder.build()) {
            List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
//            for (ReadSheet sheet : sheets) {
//                excelReader.read(sheet);
//            }
            return sheets.stream().map(ReadSheet::getSheetName).collect(Collectors.toList());
        }
    }

    public static List<ExcelHeaderOptions> getFilterDropdownValues(ExcelEntity excelEntity) {
        List<ExcelHeaderOptions> excelHeaderOptions = new ArrayList<>();
        String filePath = excelEntity.getOriginalFilePath();
        List<String> sheetNames = excelEntity.getSheetNames();
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {
            for (String sheetName : sheetNames) {
                Sheet sheet = workbook.getSheet(sheetName);
                Map<String, List<Map<String, Boolean>>> filterMap = new LinkedHashMap<>();
                // 方法1：尝试XSSF专用方法（适用于.xlsx文件）
                if (sheet instanceof XSSFSheet) {
                    XSSFSheet xssfSheet = (XSSFSheet) sheet;
                    if (xssfSheet.getCTWorksheet().getAutoFilter() != null) {
                        // 获取筛选范围
                        CellRangeAddress filterRange = CellRangeAddress.valueOf(
                                xssfSheet.getCTWorksheet().getAutoFilter().getRef()
                        );
                        // 遍历筛选列
                        Row headerRow = sheet.getRow(0);
                        if (headerRow != null) {
                            for (int col = filterRange.getFirstColumn(); col <= filterRange.getLastColumn(); col++) {
                                Cell headerCell = headerRow.getCell(col);
                                if (headerCell == null) {
                                    continue;
                                }
                                String colName = getCellValueAsString(headerCell);
                                List<String> uniqueValues = getColumnUniqueValues(sheet, col);
                                List<Map<String, Boolean>> uniqueValueList = uniqueValues.stream()
                                    .map(value -> {
                                        Map<String, Boolean> map = new HashMap<>();
                                        map.put(value, true);
                                        return map;
                                    })
                                    .collect(Collectors.toList());
                                filterMap.put(colName, uniqueValueList);
                            }
                        }
                }
                // 方法2：通用方法（适用于所有Excel文件）
                else {
                    // 检查第一行是否有筛选按钮（通过单元格样式判断）
                    Row headerRow = sheet.getRow(0);
                    if (headerRow != null) {
                        for (Cell cell : headerRow) {
                            if (cell != null && cell.getCellStyle().getFillForegroundColor() > 0) { // 简单判断筛选样式
                                String colName = getCellValueAsString(cell);
                                List<String> uniqueValues = getColumnUniqueValues(sheet, cell.getColumnIndex());
                                List<Map<String, Boolean>> uniqueValueList = uniqueValues.stream()
                                    .map(value -> {
                                        Map<String, Boolean> map = new HashMap<>();
                                        map.put(value, true);
                                        return map;
                                    })
                                    .collect(Collectors.toList());
                                filterMap.put(colName, uniqueValueList);
                            }
                        }
                    }
                }
                excelHeaderOptions.add(new ExcelHeaderOptions(sheetName ,filterMap));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return excelHeaderOptions;
    }

    private static List<String> getColumnUniqueValues(Sheet sheet, int columnIndex) {
        Set<String> uniqueValues = new LinkedHashSet<>();
        boolean hasBlankCell = false;
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                Cell cell = row.getCell(columnIndex);
                if (cell != null) {
                    String value = getCellValueAsString(cell);
                    if (value != null && !value.trim().isEmpty()) {
                        uniqueValues.add(value);
                    } else {
                        hasBlankCell = true;
                    }
                } else {
                    hasBlankCell = true;
                }
            }
        }
        List<String>  result = new ArrayList<>();
        if (hasBlankCell) {
            result.add("空白");
        }
        result.addAll(uniqueValues);
        return result;
    }

    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    // 检查是否为日期格式
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        // 处理整数和小数
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == (long) numericValue) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        switch (cell.getCachedFormulaResultType()) {
                            case STRING:
                                return cell.getStringCellValue();
                            case NUMERIC:
                                double formulaNumericValue = cell.getNumericCellValue();
                                if (formulaNumericValue == (long) formulaNumericValue) {
                                    return String.valueOf((long) formulaNumericValue);
                                } else {
                                    return String.valueOf(formulaNumericValue);
                                }
                            case BOOLEAN:
                                return String.valueOf(cell.getBooleanCellValue());
                            case BLANK:
                                return "";
                            default:
                                return "";
                        }
                    } catch (Exception e) {
                        // 如果无法获取缓存的公式结果，尝试计算公式
                        log.warn("无法获取公式单元格的缓存结果，位置: {}, 错误: {}", cell.getAddress(), e.getMessage());
                        return "";
                    }
                case BLANK:
                    return "";
                case ERROR:
                    return "#ERROR";
                default:
                    return "";
            }
        } catch (Exception e) {
            log.error("读取单元格值时发生错误，位置: {}, 错误: {}", cell.getAddress(), e.getMessage());
            return "";
        }
    }

}