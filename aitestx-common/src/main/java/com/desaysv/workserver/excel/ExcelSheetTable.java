package com.desaysv.workserver.excel;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ExcelSheetTable implements Serializable {
    private List<String> tableHeader = new ArrayList<>();
    private List<HashMap<Integer, String>> tableData = new ArrayList<>();
    private Map<String, List<Map<String, Boolean>>> tableHeaderDropDownOptions = new HashMap<>();

    public void clear() {
        tableHeader.clear();
        tableData.clear();
        tableHeaderDropDownOptions.clear();
    }

}
