package com.desaysv.workserver.entity;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

//自动升级结果通知消息类
@Data
public class UpgradeResultReportDto {
    private String projectName;
    private String version;
    private String testUnit;
    private String endTime;
    private String errorMessage;

     public UpgradeResultReportDto(){
     }

     public UpgradeResultReportDto(String projectName, String version, String testUnit, String endTime, String errorMessage){
          this.projectName = projectName;
          this.version = version;
          this.testUnit = testUnit;
          this.endTime = endTime;
          this.errorMessage = errorMessage;
     }
}
